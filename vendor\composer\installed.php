<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => null,
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => null,
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'atlas/pdo' => array(
            'pretty_version' => '1.2.0',
            'version' => '1.2.0.0',
            'reference' => 'b64a164290b93bffd2e034dc6e49535afc7dea80',
            'type' => 'library',
            'install_path' => __DIR__ . '/../atlas/pdo',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'atlas/query' => array(
            'pretty_version' => '1.3.2',
            'version' => '1.3.2.0',
            'reference' => '9ba960c3dcac619ab55da8d39c28465db973c900',
            'type' => 'library',
            'install_path' => __DIR__ . '/../atlas/query',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpsanitization/phpsanitization' => array(
            'pretty_version' => 'v2.0.0',
            'version' => '2.0.0.0',
            'reference' => 'a39377ccd2f7dbab4611efcd164e86f85065b3d1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpsanitization/phpsanitization',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'simple-crud/simple-crud' => array(
            'pretty_version' => 'v7.5.3',
            'version' => '7.5.3.0',
            'reference' => 'a1d689ad91e1c861cc555bb0aeb71956c7db032c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../simple-crud/simple-crud',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
