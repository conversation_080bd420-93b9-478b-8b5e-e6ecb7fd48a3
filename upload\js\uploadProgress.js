// Upload Progress Handler
class UploadProgressHandler {
    constructor() {
        this.progressContainer = null;
        this.progressBar = null;
        this.progressText = null;
        this.eventSource = null;
    }

    // Create progress bar HTML
    createProgressBar() {
        return `
        <div id="upload-progress" class="bg-white border border-gray-200 rounded-lg p-6 mb-6 animate-fade-in">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <div class="bg-blue-100 rounded-full p-3">
                        <i class="fas fa-cloud-upload-alt text-blue-600 text-2xl"></i>
                    </div>
                </div>
                <div class="ml-4 flex-1">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="text-lg font-semibold text-blue-800">Uploading Video</h3>
                    </div>
                    <p id="progress-message" class="text-blue-700 mb-4">
                        Preparing upload...
                    </p>
                    <div class="w-full bg-gray-200 rounded-full h-3 mb-4">
                        <div id="progress-bar" class="bg-blue-600 h-3 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <div class="text-sm text-gray-600">
                        <span id="progress-percentage">0%</span> complete
                    </div>
                </div>
            </div>
        </div>`;
    }

    // Show progress bar
    showProgress() {
        const messageContainer = document.getElementById('upload-message');
        if (messageContainer) {
            messageContainer.innerHTML = this.createProgressBar();
            this.progressContainer = document.getElementById('upload-progress');
            this.progressBar = document.getElementById('progress-bar');
            this.progressText = document.getElementById('progress-message');
            this.progressPercentage = document.getElementById('progress-percentage');
        }
    }

    // Update progress
    updateProgress(percentage, message) {
        if (this.progressBar) {
            this.progressBar.style.width = percentage + '%';
        }
        if (this.progressText) {
            this.progressText.textContent = message;
        }
        if (this.progressPercentage) {
            this.progressPercentage.textContent = percentage + '%';
        }
    }

    // Show error
    showError(error) {
        const messageContainer = document.getElementById('upload-message');
        if (messageContainer) {
            messageContainer.innerHTML = `
            <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6 animate-fade-in">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <div class="bg-red-100 rounded-full p-3">
                            <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="text-lg font-semibold text-red-800">Upload Failed</h3>
                        </div>
                        <p class="text-red-700 mb-4">
                            ${error}
                        </p>
                        <button onclick="location.reload()" class="inline-flex items-center bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition text-sm font-medium">
                            <i class="fas fa-redo mr-2"></i>
                            Try Again
                        </button>
                    </div>
                </div>
            </div>`;
        }
    }

    // Start progress tracking
    startProgressTracking() {
        this.showProgress();
        
        // Simulate progress for now - in real implementation, this would connect to server-sent events
        let progress = 0;
        const steps = [
            { percentage: 10, message: 'Validating files...' },
            { percentage: 20, message: 'Files validated successfully' },
            { percentage: 30, message: 'Starting upload to server...' },
            { percentage: 50, message: 'Uploading video file...' },
            { percentage: 70, message: 'Uploading thumbnail...' },
            { percentage: 90, message: 'Processing on server...' },
            { percentage: 100, message: 'Upload completed successfully!' }
        ];
        
        let currentStep = 0;
        const progressInterval = setInterval(() => {
            if (currentStep < steps.length) {
                this.updateProgress(steps[currentStep].percentage, steps[currentStep].message);
                currentStep++;
            } else {
                clearInterval(progressInterval);
            }
        }, 1000);
    }
}

// Initialize progress handler
const uploadProgress = new UploadProgressHandler();

// Override HTMX form submission to show progress
document.addEventListener('DOMContentLoaded', function() {
    const uploadForm = document.querySelector('form[hx-post="api/insertVideo.php"]');
    if (uploadForm) {
        uploadForm.addEventListener('htmx:beforeRequest', function(event) {
            // Start progress tracking when form is submitted
            uploadProgress.startProgressTracking();
        });
        
        uploadForm.addEventListener('htmx:responseError', function(event) {
            uploadProgress.showError('Upload failed. Please try again.');
        });
    }
});
