<?php
if (!isset($_SESSION['userId'])) {
    header("Location: ../../login");
    exit;
}

// Array of upload servers - add your server URLs here
$uploadServers = [
    'http://localhost/server1/upload.php', // Your demo API
    'http://localhost/server1/upload.php', // Your demo API
];

// Function to validate video file
function validateVideoFile($file) {
    $allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/flv', 'video/webm'];
    $maxSize = 500 * 1024 * 1024; // 500MB

    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['valid' => false, 'error' => 'File upload error occurred'];
    }

    if ($file['size'] > $maxSize) {
        return ['valid' => false, 'error' => 'Video file too large (max 500MB)'];
    }

    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);

    if (!in_array($mimeType, $allowedTypes)) {
        return ['valid' => false, 'error' => 'Invalid video format'];
    }

    return ['valid' => true];
}

// Function to validate thumbnail file
function validateThumbnailFile($file) {
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    $maxSize = 5 * 1024 * 1024; // 5MB

    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['valid' => false, 'error' => 'Thumbnail upload error occurred'];
    }

    if ($file['size'] > $maxSize) {
        return ['valid' => false, 'error' => 'Thumbnail file too large (max 5MB)'];
    }

    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);

    if (!in_array($mimeType, $allowedTypes)) {
        return ['valid' => false, 'error' => 'Invalid thumbnail format'];
    }

    return ['valid' => true];
}

// Function to upload files to server with retry mechanism
function uploadToServer($videoFile, $thumbnailFile, $servers) {
    foreach ($servers as $index => $serverUrl) {
        $ch = curl_init();

        $postData = [
            'video' => new CURLFile($videoFile['tmp_name'], $videoFile['type'], $videoFile['name']),
            'thumbnail' => new CURLFile($thumbnailFile['tmp_name'], $thumbnailFile['type'], $thumbnailFile['name']),
            'userId' => $_SESSION['userId']
        ];

        curl_setopt_array($ch, [
            CURLOPT_URL => $serverUrl,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $postData,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 300, // 5 minutes timeout
            CURLOPT_CONNECTTIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_FOLLOWLOCATION => true
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if (!$error && $httpCode == 200) {
            $responseData = json_decode($response, true);
            if ($responseData && isset($responseData['videoUrl']) && isset($responseData['thumbnailUrl'])) {
                return [
                    'success' => true,
                    'videoUrl' => $responseData['videoUrl'],
                    'thumbnailUrl' => $responseData['thumbnailUrl'],
                    'server' => $serverUrl
                ];
            }
        }

        // Log failed attempt (optional)
        error_log("Upload failed for server $serverUrl: $error (HTTP: $httpCode)");
    }

    return ['success' => false, 'error' => 'All servers failed to upload'];
}

// Function to process upload (called from insertVideo.php)
function processUpload() {
    global $uploadServers;

    // Check if files are present
    if (!isset($_FILES['video']) || !isset($_FILES['thumbnail'])) {
        return ['success' => false, 'error' => 'Video and thumbnail files are required'];
    }

    $videoFile = $_FILES['video'];
    $thumbnailFile = $_FILES['thumbnail'];

    // Validate video file
    $videoValidation = validateVideoFile($videoFile);
    if (!$videoValidation['valid']) {
        return ['success' => false, 'error' => $videoValidation['error']];
    }

    // Validate thumbnail file
    $thumbnailValidation = validateThumbnailFile($thumbnailFile);
    if (!$thumbnailValidation['valid']) {
        return ['success' => false, 'error' => $thumbnailValidation['error']];
    }

    // Upload to servers
    $uploadResult = uploadToServer($videoFile, $thumbnailFile, $uploadServers);

    return $uploadResult;
}

// Function to process upload for editing (called from editVideo/api/updateVideo.php)
// Only handles thumbnail uploads - video files cannot be changed during editing
function processEditUpload($currentVideo) {
    global $uploadServers;

    $result = ['success' => true];
    $hasThumbnailFile = isset($_FILES['thumbnail']) && $_FILES['thumbnail']['error'] === UPLOAD_ERR_OK;

    // If no thumbnail uploaded, return success (keeping current thumbnail URL)
    if (!$hasThumbnailFile) {
        return ['success' => true, 'message' => 'No thumbnail to upload'];
    }

    // Validate uploaded thumbnail
    $thumbnailValidation = validateThumbnailFile($_FILES['thumbnail']);
    if (!$thumbnailValidation['valid']) {
        return ['success' => false, 'error' => $thumbnailValidation['error']];
    }

    // Upload thumbnail to servers
    foreach ($uploadServers as $index => $serverUrl) {
        $ch = curl_init();

        $postData = [
            'userId' => $_SESSION['userId'],
            'thumbnail' => new CURLFile($_FILES['thumbnail']['tmp_name'], $_FILES['thumbnail']['type'], $_FILES['thumbnail']['name'])
        ];

        curl_setopt_array($ch, [
            CURLOPT_URL => $serverUrl,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $postData,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 300,
            CURLOPT_CONNECTTIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_FOLLOWLOCATION => true
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if (!$error && $httpCode == 200) {
            $responseData = json_decode($response, true);
            if ($responseData && $responseData['success']) {
                // Only update thumbnail URL (video URL is not changed during editing)
                if (isset($responseData['thumbnailUrl'])) {
                    $result['thumbnailUrl'] = $responseData['thumbnailUrl'];
                }
                $result['server'] = $serverUrl;
                return $result;
            }
        }

        // Log failed attempt
        error_log("Edit thumbnail upload failed for server $serverUrl: $error (HTTP: $httpCode)");
    }

    return ['success' => false, 'error' => 'All servers failed to upload thumbnail'];
}

?>