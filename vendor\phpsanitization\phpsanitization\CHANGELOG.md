# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/), and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## v2.0.0 (2025-05-03)

### Added
- Strict typing throughout the library with `declare(strict_types=1)`
- Comprehensive parameter and return type declarations for all methods
- Support for PHP 8 features including union types and the match expression
- Enhanced email validation with DNS checking and customizable provider lists
- Recursive sanitization for deeply nested arrays
- Better type preservation for non-string values in arrays
- Support for method chaining on setter methods
- Proper exception handling for invalid callbacks
- Additional unit tests for all new functionality

### Changed
- Modernized library description and keywords in composer.json
- Improved PHPDoc comments with modern standards and better descriptions
- Updated utility methods with more robust edge case handling
- Refactored code to use modern PHP patterns and reduce duplication
- Enhanced SQL escaping using `strtr()` instead of `str_replace()`
- Better isEmpty() checks that handle whitespace strings properly

### Fixed
- Arrays with mixed data types are now properly sanitized
- Empty array handling in isAssociative() method
- Various edge cases in input validation

### Security
- Improved HTML entity encoding with ENT_HTML5 flag
- Enhanced input validation across all methods
- Better protection against XSS and SQL injection attacks

## v1.0.0

Initial Release

## v1.0.1

Better Code Structure and Added Tests

## v1.0.2

Better code structure and new test cases

## v1.0.3

1. Added Github CI ( [#1](https://github.com/fariscode511/PhpSanitization/pull/1) )
2. Added PHP_Codesniffer as a dependency

## v1.0.4

1. Added Examples
2. Added Library to PHPClasses
3. Fixed Documentation Issues

## v1.0.5

1. Updated Workflow
2. Added Composer Normalize as a dependency
3. Cleaner File Structure

## v1.0.6

1. Updated Workflow
2. Fixed Examples
3. Added composer-require-checker
4. Updated Documentation
5. Added Make file

## 1.0.7

1. Added Reusable Functions
2. Cleaner Code Structure
3. Added New Tests
4. Added New Examples
5. Updated Docs

## 1.0.8

1. Added Static Code Analysis
2. Added Mutation Tests
3. Added Code Coverage
4. Fixed composer-require-checker phar file
5. Enabled DependaBot

## v1.0.9

1. Added getter and setter for $data
2. The class is now final
3. Fixed some code violations
4. Added New Test Cases
5. Added New Examples
6. Updated Docs

## v1.0.10

1. Added usePregReplace and useStrReplace for custom sanitization
2. Added isValid Function to validate any type of data
3. Added Simple Email Validation
4. Fixed code violations
5. Added New Test Cases
6. Added New Examples
7. Updated Docs

## v1.0.11

Just some bug fixes and cleaning

## v1.0.12

1. Added callback function support
2. Added Example for callback functions
3. Added Test case for callback functions

## v1.0.13

Just some bug fixes, cleaning, and refactoring


