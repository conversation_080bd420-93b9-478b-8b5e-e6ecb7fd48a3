<?php

require_once '../../includes/config.php';

//using simple crud fetch videos from db and send in card like interface
$channelId = $_POST['cid'];
$userId = $_SESSION['userId'];
//check if person already subscribe
if($db->subscriptions->get(['uid' => $userId, 'cid' => $channelId])){
    echo '<button class="unsubscribe-btn bg-red-600 text-white px-6 py-2 rounded-full hover:bg-red-700 transition font-medium" 
          hx-post="'.$mainUrl.'mainApis/unSubscribe.php" 
          hx-vals=\'{"id":"'.$channelId.'"}\' 
          hx-target="this" 
          hx-trigger="click" 
          hx-swap="outerHTML">
        <i class="fas fa-user-minus mr-1"></i>
        Unsubscribe
    </button>';
    exit;
}
$subscribers = $db->subscriptions
    ->selectAggregate('COUNT')
    ->where('cid =', $channelId)
    ->get();
// add sucsription
if($db->subscriptions[] = [
    'uid' => $userId,
    'cid' => $channelId
]) {
    echo '<button class="unsubscribe-btn bg-red-600 text-white px-6 py-2 rounded-full hover:bg-red-700 transition font-medium" 
          hx-post="'.$mainUrl.'mainApis/unSubscribe.php" 
          hx-vals=\'{"id":"'.$channelId.'"}\' 
          hx-target="this" 
          hx-trigger="click" 
          hx-swap="outerHTML">
        <i class="fas fa-user-minus mr-1"></i>
        Unsubscribe
    </button>';
}

?>