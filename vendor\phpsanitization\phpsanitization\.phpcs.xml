<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="vendor/squizlabs/php_codesniffer/phpcs.xsd">

    <arg name="basepath" value="."/>
    <arg name="colors"/>
    <arg name="extensions" value="php"/>
    <arg name="parallel" value="75"/>
    
    <!-- Show progress -->
    <arg value="np"/>

    <!-- Paths to check -->
    <file>src</file>

    <!-- Don't hide tokenizer exceptions -->
    <rule ref="Internal.Tokenizer.Exception">
        <type>error</type>
    </rule>

    <!-- Include all rules from the PSR-12 Coding Standard -->
    <rule ref="PSR12"/>
</ruleset>
