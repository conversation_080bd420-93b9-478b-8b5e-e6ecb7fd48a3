<!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row justify-between items-center h-auto md:h-16 py-4 md:py-0">
                <!-- Logo -->
                <div class="flex items-center space-x-4 mb-4 md:mb-0">
                    <a href="/social" class="flex items-center space-x-2">
                        <i class="fas fa-moon text-green-600 text-2xl"></i>
                        <h1 class="text-xl font-bold text-gray-900">Better Muslim</h1>
                    </a>
                </div>
                
                <?php if (!isset($upload)) { ?>
                <!-- Search Bar -->
                <div class="flex-1 w-full md:max-w-2xl md:mx-8 mb-4 md:mb-0">
                    <div class="relative">
                        <input 
                            hx-post="<?php echo $mainUrl;?>mainApis/fetchVideos.php" 
                            hx-trigger="keyup[key=='Enter']" 
                            hx-target="#mainContent" 
                            type="text" 
                            hx-swap="innetHTML" 
                            name="search" 
                            id="search" 
                            placeholder="Search for videos..." 
                            class="w-full px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-green-500"
                        >
                        <button class="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <?php } ?>

                <!-- Right Side Menu -->
                <div class="flex items-center space-x-2 md:space-x-4">
                <!--PWA install button-->
                <button id="pwa-install-btn" style="display: none;" class="bg-green-600 text-white px-3 md:px-4 py-2 rounded-lg hover:bg-green-700 transition text-sm md:text-base" title="Install Better Muslim App">
                    <i class="fas fa-download mr-1 md:mr-2"></i><span class="hidden md:inline">Install App</span>
                </button>
                <script>
                    let deferredPrompt;
                    
                    window.addEventListener('beforeinstallprompt', (e) => {
                        e.preventDefault();
                        deferredPrompt = e;
                        // Show the install button
                        document.getElementById('pwa-install-btn').style.display = 'flex';
                        
                        // Add click handler for install button
                        document.getElementById('pwa-install-btn').addEventListener('click', async () => {
                            if (deferredPrompt) {
                                deferredPrompt.prompt();
                                const { outcome } = await deferredPrompt.userChoice;
                                if (outcome === 'accepted') {
                                    document.getElementById('pwa-install-btn').style.display = 'none';
                                }
                                deferredPrompt = null;
                            }
                        });
                    });

                    // Hide button if PWA is already installed
                    window.addEventListener('appinstalled', () => {
                        document.getElementById('pwa-install-btn').style.display = 'none';
                    });
                </script>
                <!--Upload button-->
                    <a href="<?php echo $mainUrl;?>upload" class="bg-green-600 text-white px-3 md:px-4 py-2 rounded-lg hover:bg-green-700 transition text-sm md:text-base">
                        <i class="fas fa-plus mr-1 md:mr-2"></i><span class="hidden md:inline">Upload</span>
                    </a>
                    <?php
                    if (isset($_SESSION['userId'])) {
                        $user = $db->user[$_SESSION['userId']];
                    } else {
                        $user = new stdClass();
                        $user->avatar = 'assets/avatar.jpg';
                    }
                    ?>
                    <a href="<?php echo $mainUrl;?>account" class="text-gray-600 hover:text-gray-900">
                        <img src="<?php echo $mainUrl;?><?php echo $user->avatar;?>" alt="User" class="w-8 h-8 rounded-full">
                    </a>
                    <?php if (isset($_SESSION['userEmail'])) { ?>
                        <a href="<?php echo $mainUrl;?>mainApis/logout.php" class="text-green-600 hover:text-green-700 font-medium text-sm md:text-base">Logout</a>
                    <?php } else { ?>
                        <a href="<?php echo $mainUrl;?>login" class="text-green-600 hover:text-green-700 font-medium text-sm md:text-base">Login</a>
                    <?php } ?>
                </div>
            </div>
        </div>
    </header>
