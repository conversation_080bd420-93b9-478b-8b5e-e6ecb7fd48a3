<?php
require_once '../includes/config.php';
$title = 'Better Muslim';
include '../includes/header.php';
if (!isset($_GET['id'])) {
    header("Location: ../");
}else{
$channelId = $_GET['id'];
}

$channel = $db->user[$channelId];
   //format subscribers
$subscribers = $db->subscriptions
    ->selectAggregate('COUNT')
    ->where('cid =', $channelId)
    ->get();
    if ($subscribers >= 1000000) {
        $formattedSubscribers = number_format($subscribers/1000000, 1) . 'M';
    } else if ($subscribers >= 1000) {
        $formattedSubscribers = number_format($subscribers/1000, 1) . 'K';
    } else {
        $formattedSubscribers = $subscribers;
    }
       //format subscribers
$videosNumber = $db->video
    ->selectAggregate('COUNT')
    ->where('uid =', $channelId)
    ->get();
   //convert date (2025-07-08) to string (July 2025)
    if ($channel->dateJoined instanceof DateTime) {
        $dateJoined = $channel->dateJoined->format('F Y');
    } else {
        $dateJoined = date('F Y', strtotime($channel->dateJoined));
    }
     if(!isset($_SESSION['userId'])){
        $subscribeButton = '<button class="bg-green-600 text-white px-6 py-2 rounded-full hover:bg-green-700 transition" onclick="showLoginPrompt()">
        Subscribe
    </button>
    <div id="loginPromptModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white p-6 rounded-lg shadow-xl max-w-sm w-full mx-4">
            <h3 class="text-xl font-semibold mb-4">Subscribe to Channel</h3>
            <p class="text-gray-600 mb-6">Want to subscribe to this channel? Please sign in to continue.</p>
            <div class="flex justify-end space-x-4">
                <button onclick="closeLoginPrompt()" class="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                <button onclick="window.location.href=\'../login\'" class="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700">Sign In</button>
            </div>
        </div>
    </div>
    <script>
        function showLoginPrompt() {
            document.getElementById("loginPromptModal").classList.remove("hidden");
        }
        function closeLoginPrompt() {
            document.getElementById("loginPromptModal").classList.add("hidden");
        }
    </script>';
    }else{
    $channelId = $_GET['id'];
    $userId = $_SESSION['userId'];
    if($db->subscriptions->get(['uid' => $userId, 'cid' => $channelId])){
        $subscribeButton = '<button class="unsubscribe-btn bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition text-sm font-medium" 
          hx-post="'.$mainUrl.'mainApis/unSubscribe.php" 
          hx-vals=\'{"id":"'.$channelId.'"}\' 
          hx-target="this" 
          hx-trigger="click" 
          hx-swap="outerHTML">
        <i class="fas fa-user-minus mr-1"></i>
        Unsubscribe
    </button>';
    }else{
        $subscribeButton = '<button class="bg-green-600 text-white px-6 py-2 rounded-full hover:bg-green-700 transition" hx-post="'.$mainUrl.'watch/api/subscribe.php" hx-vals=\'{"cid":"'.$channelId.'"}\' hx-target="this" hx-trigger="click" hx-swap="outerHTML">
                <i class="fas fa-plus mr-1"></i>
            Subscribe
        </button>';
    }
}
?>

<body class="bg-gray-50">
   <?php include '../includes/navbar.php'; ?>
<main id="mainContent" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    

    <!-- Channel Info -->
    <div class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-12">
                <div class="flex items-start space-x-6">
                    <!-- Channel Avatar -->
                    <div>
                        <img src="<?php echo $mainUrl;?><?php echo $channel->avatar;?>" alt="Channel Avatar" 
                             class="w-24 h-24 md:w-32 md:h-32 rounded-full border-4 border-white bg-white shadow-lg">
                    </div>
                    
                    <div class="flex-1">
                        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                            <div class="mb-4 md:mb-0">
                                <h1 class="text-2xl md:text-3xl font-bold text-gray-900 mb-2"><?php echo $channel->name;?></h1>
                                <div class="flex flex-col md:flex-row md:items-center md:space-x-4 text-gray-600 mb-2">
                                    <span class="flex items-center mb-2 md:mb-0">
                                        <i class="fas fa-users mr-1"></i>
                                        <?php echo $formattedSubscribers;?> subscribers
                                    </span>
                                    <span class="flex items-center mb-2 md:mb-0">
                                        <i class="fas fa-video mr-1"></i>
                                        <?php echo $videosNumber;?> videos
                                    </span>
                                    <span class="flex items-center">
                                        <i class="fas fa-calendar mr-1"></i>
                                        Joined <?php echo $dateJoined;?>
                                    </span>
                                </div>
                                <p class="text-gray-700 max-w-2xl">
                                    <?php echo $channel->description;?>
                                </p>
                            </div>
                            
                            <div class="flex items-center space-x-3">
                                <?php echo $subscribeButton;?>
                                <button class="hidden border border-gray-300 text-gray-700 px-6 py-3 rounded-full hover:bg-gray-50 transition">
                                    <i class="fas fa-share mr-2"></i>
                                    Share
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Channel Navigation -->
    <div class="hidden bg-white border-b sticky top-0 z-10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <nav class="flex space-x-8">
                <a href="#" class="py-4 px-1 border-b-2 border-green-600 text-green-600 font-medium">Videos</a>
                <a href="#" class="py-4 px-1 text-gray-600 hover:text-gray-900">Playlists</a>
                <a href="#" class="py-4 px-1 text-gray-600 hover:text-gray-900">Community</a>
                <a href="#" class="py-4 px-1 text-gray-600 hover:text-gray-900">About</a>
            </nav>
        </div>
    </div>
<div class="flex justify-center pt-8">
    <div class="relative w-full max-w-2xl mx-auto">
        <input hx-post="<?php echo $mainUrl;?>mainApis/fetchVideos.php" hx-trigger="keyup[key=='Enter']" hx-target="#secondaryContent" hx-vals='{"channelId": <?php echo $channelId;?>}' type="text" hx-swap="innetHTML" name="search" id="search" placeholder="Search in this Channel..." class="w-full px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-green-500">
        <button class="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600">
            <i class="fas fa-search"></i>
        </button>
    </div>
</div>
    <!-- Channel Content -->
    <main hx-post="<?php echo $mainUrl;?>mainApis/fetchVideos.php" hx-target="this" hx-vals='{"channelId": <?php echo $channelId;?>}' hx-trigger="load" hx-swap="innerHTML" id="secondaryContent"  class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
    </main>
</main>
   
<?php include '../includes/footer.php'; ?>