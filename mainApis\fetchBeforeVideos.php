<?php

include '../includes/config.php';
if (!isset($_SESSION['userId'])) {
    header("Location: ../login");
}

$userId = $_SESSION['userId'];
if(isset($_POST['currentBeforeVideo'])){
    $currentBeforeVideo = $_POST['currentBeforeVideo'];
}
if(isset($_POST['videoId'])){
    $videoId = $_POST['videoId'];
}
if(isset($_POST['search']) && isset($_POST['currentBeforeVideo'])){
    $search = '%' . $_POST['search'] . '%';
    $stmt = $pdo->prepare("SELECT * FROM video WHERE title LIKE :search AND uid = :channelId AND id != :videoId ORDER BY RAND() LIMIT 24");
    $stmt->bindParam(':videoId', $videoId, PDO::PARAM_INT);
    $stmt->bindParam(':channelId', $userId, PDO::PARAM_INT);
    $stmt->bindParam(':search', $search, PDO::PARAM_STR);
    $stmt->execute();
    $videos = $stmt->fetchAll(PDO::FETCH_OBJ);
}elseif(isset($_POST['search'])){
 $search = '%' . $_POST['search'] . '%';
    $stmt = $pdo->prepare("SELECT * FROM video WHERE title LIKE :search AND uid = :channelId  ORDER BY RAND() LIMIT 24");
    $stmt->bindParam(':channelId', $userId, PDO::PARAM_INT);
    $stmt->bindParam(':search', $search, PDO::PARAM_STR);
    $stmt->execute();
    $videos = $stmt->fetchAll(PDO::FETCH_OBJ);
}else{
    $videos = $db->video->select()    
        ->where('uid =', $userId)
        ->orderBy('RAND()')
        ->limit(24)    
        ->get();
}

if (empty($videos) || $videos == "[]") {
    echo '<div id="noResults" class="hidden col-span-2 text-center py-8">
                                        <i class="fas fa-search text-gray-400 text-3xl mb-3"></i>
                                        <h4 class="text-lg font-medium text-gray-900 mb-2">No videos found</h4>
                                        <p class="text-gray-600">Try adjusting your search terms or filters</p>
                                    </div>';
    exit;
}

foreach ($videos as $video) {
//views
$viewCount = $video->views;

if ($viewCount >= 1000000) {
    $formattedViews = number_format($viewCount/1000000, 1) . 'M';
} else if ($viewCount >= 1000) {
    $formattedViews = number_format($viewCount/1000, 1) . 'K';
} else {
    $formattedViews = $viewCount;
}

// Format upload time
$uploadDate = $video->upload;
// Convert string to DateTime object if needed (when using PDO search)
if (is_string($uploadDate)) {
    $uploadDate = new DateTime($uploadDate);
}
$now = new DateTime();
$interval = $now->diff($uploadDate);

if ($interval->y > 0) {
    $timeAgo = $interval->y . ' year' . ($interval->y > 1 ? 's' : '') . ' ago';
} elseif ($interval->m > 0) {
    $timeAgo = $interval->m . ' month' . ($interval->m > 1 ? 's' : '') . ' ago';
} elseif ($interval->d > 6) {
    $weeks = floor($interval->d / 7);
    $timeAgo = $weeks . ' week' . ($weeks > 1 ? 's' : '') . ' ago';
} elseif ($interval->d > 0) {
    $timeAgo = $interval->d . ' day' . ($interval->d > 1 ? 's' : '') . ' ago';
}else {
    $timeAgo = 'less than a day ago';
}
$user = $db->user[$video->uid];
// category
$category = $video->category;
if ($category == 0) {
    $category = '';
} elseif ($category == 1) {
    $category = '📚 Ilm';
} elseif ($category == 2) {
    $category = '💪 Fitness';
} elseif ($category == 3) {
    $category = '🛠️ Skills';
}
    ?>

     <!-- Video Card 1 -->
                                    <div class="video-card bg-white rounded-lg border border-gray-200 hover:border-indigo-300 transition-colors cursor-pointer" 
                                       >
                                        <div class="flex items-start space-x-3 p-3">
                                            <div class="relative">
                                                <img src="<?php echo $video->thumbnailUrl;?>" alt="Video thumbnail"
                                                    class="w-20 h-15 object-cover rounded border">
                                                <div class="absolute bottom-1 right-1 bg-black bg-opacity-75 text-white text-xs px-1 rounded">
                                                    <?php echo $video->duration;?>
                                                </div>
                                                <!-- Selection Checkbox -->
                                                <div class="absolute -top-2 -right-2">
                                                    <input type="checkbox" class="video-checkbox hidden" name="playlistVideos[]" value="video1">
                                                    <div class="selection-indicator w-5 h-5 border-2 border-gray-300 rounded-full bg-white hidden">
                                                        <i class="fas fa-check text-indigo-600 text-xs"></i>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <h4 class="font-medium text-gray-900 text-sm line-clamp-2 mb-1">
                                                    <?php echo $video->title;?>
                                                </h4>
                                                <div class="flex items-center space-x-3 text-xs text-gray-500">
                                                    <span class="flex items-center">
                                                        <i class="fas fa-eye mr-1"></i>
                                                        <?php echo $formattedViews;?> views
                                                    </span>
                                                    <span class="flex items-center">
                                                        <i class="fas fa-calendar mr-1"></i>
                                                        <?php echo $timeAgo;?>
                                                    </span>
                                                </div>
                                                <div class="mt-1">
                                                    <?php if(isset($currentBeforeVideo) && $currentBeforeVideo == $video->id){?>
                                                        <input type="checkbox" class="video-checkbox" name="beforeVideo" value="<?php echo $video->id;?>" checked><label class="inline-block px-2 py-0.5 bg-green-100 text-green-800 text-xs rounded-full">This video</label>
                                                        <?php }else{?>
                                                            <input type="checkbox" class="video-checkbox" name="beforeVideo" value="<?php echo $video->id;?>"><label class="inline-block px-2 py-0.5 bg-green-100 text-green-800 text-xs rounded-full">This video</label>
                                                        <?php }?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
<?php

}
?>