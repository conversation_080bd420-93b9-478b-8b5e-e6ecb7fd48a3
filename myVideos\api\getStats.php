<?php

include '../../includes/config.php';
if (!isset($_SESSION['userId'])) {
    header("Location: ../../login");
}
$userId = $_SESSION['userId'];
  //using simple crud fetch videos from db and send in card like interface
 $video =  $db->video->get(['uid' => $userId]);
 
    // Format view count
    $viewCount = $video->views;
    if ($viewCount >= 1000000) {
        $formattedViews = number_format($viewCount/1000000, 1) . 'M';
    } else if ($viewCount >= 1000) {
        $formattedViews = number_format($viewCount/1000, 1) . 'K';
    } else {
        $formattedViews = $viewCount;
    }
$videoNumber = $db->video
    ->selectAggregate('COUNT')
    ->where('uid =', $userId)
    ->get();
    $subscribers = $db->subscriptions
    ->selectAggregate('COUNT')
    ->where('uid =', $userId)
    ->get();
    if ($subscribers >= 1000000) {
        $formattedSubscribers = number_format($subscribers/1000000, 1) . 'M';
    } else if ($subscribers >= 1000) {
        $formattedSubscribers = number_format($subscribers/1000, 1) . 'K';
    } else {
        $formattedSubscribers = $subscribers;
    }
?>

   <div class="bg-white rounded-lg p-6 shadow-sm">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">Total Videos</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $videoNumber;?></p>
                    </div>
                    <i class="fas fa-video text-green-600 text-2xl"></i>
                </div>
            </div>
            <div class="bg-white rounded-lg p-6 shadow-sm">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">Total Views</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $formattedViews;?></p>
                    </div>
                    <i class="fas fa-eye text-blue-600 text-2xl"></i>
                </div>
            </div>
            <div class="bg-white rounded-lg p-6 shadow-sm">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-600">Total Subscribers</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $formattedSubscribers;?></p>
                    </div>
                    <i class="fas fa-user text-red-600 text-2xl"></i>
                </div>
            </div>
   
