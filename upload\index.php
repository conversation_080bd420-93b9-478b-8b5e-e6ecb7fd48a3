<?php
require_once '../includes/config.php';
$title = 'Upload Video - Better Muslim';
include '../includes/header.php';
if (isset($_SESSION['userId'])) {
} else {
   header("Location: ../login");
}
?>
<body class="bg-gray-50">
    <?php
    $upload = true;
    ?>
    <?php include '../includes/navbar.php'; ?>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white rounded-lg shadow-sm   " id="upload-message">
            <!-- Enhanced Upload Form -->
            <div class="max-w-4xl mx-auto bg-white rounded-xl shadow-lg overflow-hidden">
                <!-- Form Header -->
                <div class="bg-gradient-to-r from-green-600 to-blue-600 px-8 py-6">
                    <div class="flex items-center space-x-3">
                        <div class="bg-white bg-opacity-20 rounded-full p-3">
                            <i class="fas fa-cloud-upload-alt text-white text-2xl"></i>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold text-white">Upload Islamic Video</h2>
                            <p class="text-green-100">Share beneficial knowledge with the Ummah</p>
                        </div>
                    </div>
                </div>

                <!-- Form Content -->
                <div class="p-8">
                    <form class="space-y-8" hx-post="api/insertVideo.php"  hx-encoding='multipart/form-data'  hx-target="#upload-message" hx-swap="innerHTML">
                        <!-- Video Details Section -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <i class="fas fa-video text-green-600 mr-2"></i>
                                Video Information
                            </h3>
                            
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <!-- Video File -->
                                <div class="lg:col-span-2">
                                    <label for="video" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-video text-green-600 mr-1"></i>
                                        Video File *
                                    </label>
                                    <div class="relative">
                                        <input type="file" id="video" name="video" accept="video/*" required
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1">Upload your video file (Max: 500MB, Formats: MP4, AVI, MOV, WMV, FLV, WebM)</p>
                                </div>

                                <!-- Thumbnail File -->
                                <div class="lg:col-span-2">
                                    <label for="thumbnail" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-image text-green-600 mr-1"></i>
                                        Thumbnail Image *
                                    </label>
                                    <div class="relative">
                                        <input type="file" id="thumbnail" name="thumbnail" accept="image/*" required
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1">Upload thumbnail image (Max: 5MB, Formats: JPEG, PNG, GIF, WebP, Recommended: 1280x720)</p>
                                </div>
                                 <div class="lg:col-span-2">
                                    <label for="thumbnailUrl" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-clock text-green-600 mr-1"></i>
                                        Duration *
                                    </label>
                                    <div class="relative">
                                        <input type="text" id="duration" name="duration" required
                                            class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                                            placeholder="1:15:30">
                                        <i class="fas fa-clock absolute left-3 top-3.5 text-gray-400"></i>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1">In this format 15:30.</p>
                                </div>
                                
                            </div>
                        </div>

                        <!-- Content Details Section -->
                        <div class="bg-blue-50 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <i class="fas fa-edit text-blue-600 mr-2"></i>
                                Content Details
                            </h3>
                            
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <!-- Title -->
                                <div class="lg:col-span-2">
                                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-heading text-blue-600 mr-1"></i>
                                        Video Title *
                                    </label>
                                    <input type="text" id="title" name="title" required
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                        placeholder="Enter an engaging title for your video"
                                        maxlength="130">
                                    <div class="flex justify-between mt-1">
                                        <p class="text-xs text-gray-500">Make it descriptive and engaging</p>
                                        <span class="text-xs text-gray-400" id="title-counter">0/130</span>
                                    </div>
                                </div>

                                <!-- Category -->
                                <div>
                                    <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-tags text-blue-600 mr-1"></i>
                                        Category *
                                    </label>
                                    <div class="relative">
                                        <select id="category" name="category" required
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors appearance-none bg-white">
                                            <option value="">Choose a category</option>
                                            <option value="1">📚 Ilm (Islamic Knowledge)</option>
                                            <option value="2">💪 Fitness</option>
                                            <option value="3">🛠️ Skills</option>
                                        </select>
                                        <i class="fas fa-chevron-down absolute right-3 top-3.5 text-gray-400 pointer-events-none"></i>
                                    </div>
                                </div>

                                <!-- Description -->
                                <div class="lg:col-span-2">
                                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-align-left text-blue-600 mr-1"></i>
                                        Description
                                    </label>
                                    <textarea id="description" name="description" rows="4"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none transition-colors"
                                            placeholder="Describe your video content... What will viewers learn? Include relevant Islamic context if applicable."
                                            maxlength="2000"></textarea>
                                    <div class="flex justify-between mt-1">
                                        <p class="text-xs text-gray-500">Provide context and key points covered in your video</p>
                                        <span class="text-xs text-gray-400" id="desc-counter">0/2000</span>
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                        <!-- Playlist Search Section -->
                        <div class="bg-indigo-50 rounded-lg p-6 ">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <i class="fas fa-list-ul text-indigo-600 mr-2"></i>
                                Video Before This One In a Series
                            </h3>
                            
                            <!-- Search Bar -->
                            <div class="mb-6">
                                <div class="relative">
                                    <input type="text" id="playlistSearch" 
                                        class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                                        placeholder="Search your videos to add to playlist..."
                                        hx-post="<?php echo $mainUrl;?>mainApis/fetchBeforeVideos.php"
                                        hx-target="#videoGrid"
                                        hx-trigger="keyup changed delay:500ms"
                                        hx-swap="innerHTML"
                                        name="search"
                                        >
                                    <i class="fas fa-search absolute left-3 top-3.5 text-gray-400"></i>
                                </div>
                                <div class="flex gap-10" >
                                    <div>
                                    <p class="text-xs text-gray-500 mt-2">Find and select a video that comes before this one from your channel</p>
                                    </div>
                                    <div class="mt-1">
                                    <input type="checkbox" class="video-checkbox" name="beforeVideo" value="0"><label class="inline-block px-2 py-0.5 bg-red-100 ml-1 text-red-800 text-xs rounded-full">This video is not part of a series</label>
                                    </div>
                                </div>
                            </div>

                        

                            <!-- Video Grid -->
                            <div class="max-h-96 overflow-y-auto border border-gray-200 rounded-lg">
                                <div id="videoGrid" hx-post="<?php echo $mainUrl;?>mainApis/fetchBeforeVideos.php" hx-target="#videoGrid" hx-trigger="load" hx-swap="innerHTML" class="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
                                    
                               


                                    <!-- No Results Message -->
                                    
                                </div>
                            </div>

                            <!-- Selected Videos Summary -->
                            <div id="selectedSummary" class="hidden mt-4 p-4 bg-indigo-100 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <i class="fas fa-check-circle text-indigo-600 mr-2"></i>
                                        <span class="font-medium text-indigo-900">
                                            <span id="selectedCount">0</span> video(s) selected for playlist
                                        </span>
                                    </div>
                                    <button onclick="clearSelection()" class="text-indigo-600 hover:text-indigo-800 text-sm">
                                        <i class="fas fa-times mr-1"></i>
                                        Clear Selection
                                    </button>
                                </div>
                                <div id="selectedVideosList" class="mt-2 text-sm text-indigo-700"></div>
                            </div>
                        </div>
                        <!-- Islamic Guidelines -->
                        <div class="bg-green-50 border-l-4 border-green-400 p-6 rounded-r-lg">
                            <div class="flex items-start">
                                <i class="fas fa-mosque text-green-600 text-xl mt-1 mr-3"></i>
                                <div>
                                    <h4 class="text-sm font-semibold text-green-800 mb-2">Islamic Content Guidelines</h4>
                                    <ul class="text-sm text-green-700 space-y-1">
                                        <li class="flex items-start">
                                            <i class="fas fa-check-circle text-green-600 mr-2 mt-0.5 text-xs"></i>
                                            Content should be beneficial and align with Islamic teachings
                                        </li>
                                        <li class="flex items-start">
                                            <i class="fas fa-check-circle text-green-600 mr-2 mt-0.5 text-xs"></i>
                                            Verify authenticity of religious information shared
                                        </li>
                                        <li class="flex items-start">
                                            <i class="fas fa-check-circle text-green-600 mr-2 mt-0.5 text-xs"></i>
                                            Maintain proper Islamic etiquette and respect
                                        </li>
                                        <li class="flex items-start">
                                            <i class="fas fa-check-circle text-green-600 mr-2 mt-0.5 text-xs"></i>
                                            Avoid content that contradicts Islamic values
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex flex-col sm:flex-row justify-between items-center pt-6 border-t border-gray-200 space-y-4 sm:space-y-0">
                            <div class="flex items-center space-x-2 text-sm text-gray-600">
                                <i class="fas fa-info-circle text-blue-500"></i>
                                <span>All fields marked with * are required</span>
                            </div>
                            
                            <div class="flex space-x-4">
                                <button type="button" class="hidden px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors font-medium">
                                    <i class="fas fa-save mr-2"></i>
                                    Save Draft
                                </button>
                                <button type="submit" class="bg-gradient-to-r from-green-600 to-green-700 text-white px-8 py-3 rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                                    <i class="fas fa-cloud-upload-alt mr-2"></i>
                                    Upload Video
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Upload Output -->
                    <div id="upload-output" class="mt-6"></div>
                </div>
            </div>

        <!-- JvaScript for Character Counters -->
            <script>
                // Ensure only one checkbox can be selected at a time
                document.addEventListener('change', function(e) {
                    if (e.target.classList.contains('video-checkbox')) {
                        const checkboxes = document.querySelectorAll('.video-checkbox');
                        checkboxes.forEach(checkbox => {
                            if (checkbox !== e.target) {
                                checkbox.checked = false;
                            }
                        });
                    }
                });
                    document.addEventListener('DOMContentLoaded', function() {
                        // Title character counter
                        const titleInput = document.getElementById('title');
                        const titleCounter = document.getElementById('title-counter');
                        
                        titleInput.addEventListener('input', function() {
                            const length = this.value.length;
                            titleCounter.textContent = `${length}/130`;
                            titleCounter.className = length > 120 ? 'text-xs text-red-500' : 'text-xs text-gray-400';
                        });
                        
                        // Description character counter
                        const descInput = document.getElementById('description');
                        const descCounter = document.getElementById('desc-counter');
                        
                        descInput.addEventListener('input', function() {
                            const length = this.value.length;
                            descCounter.textContent = `${length}/2000`;
                            descCounter.className = length > 1950 ? 'text-xs text-red-500' : 'text-xs text-gray-400';
                        });
                    });
            </script>

            <!-- Include Upload Progress JavaScript -->
            <script src="js/uploadProgress.js"></script>

            <div id="upload-output"></div>
        </div>
    </main>
<?php include '../includes/footer.php'; ?>