<?php
require_once '../includes/config.php';
$title = 'Register - Better Muslim';
include '../includes/header.php';
if (isset($_SESSION['userEmail'])) {
    header("Location: ../");
}   
?>
<body class="bg-gradient-to-br from-green-50 to-blue-50 min-h-screen">
    <?php
    $upload = true;
    include '../includes/navbar.php'; ?>
    <!-- Main Content -->
    <main class="flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div class="text-center">
                <i class="fas fa-moon text-green-600 text-4xl mb-4"></i>
                <h2 class="text-3xl font-bold text-gray-900 mb-2">Register</h2>
                <p class="text-gray-600">Create your account to enjoy IslamTube</p>
            </div>

            <div class="bg-white rounded-lg shadow-lg p-8">
                <form class="space-y-6" hx-post="api/createUser.php" hx-target="#register-output" hx-swap="innerHTML">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            Name 
                        </label>
                        <input type="text" id="name" name="name" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                               placeholder="Enter your name">
                             
                    </div>

                     <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            Email Address
                        </label>
                        <input type="email" id="email" name="email" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                               placeholder="Enter your email">
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                            Password
                        </label>
                        <div class="relative">
                            <input type="password" id="password" name="password" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                   placeholder="Enter your password">
                            <button type="button" class="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

               
                    <button type="submit" 
                            class="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition duration-200">
                        Sign Up
                    </button>

                 

                  
                </form>
            <div id="register-output" class="mt-4 text-center">
</div>
                <div class="mt-6 text-center">
                    <p class="text-sm text-gray-600">
                        Have an account? 
                        <a href="<?php echo $mainUrl;?>login" class="text-green-600 hover:text-green-700 font-medium">Sign in here</a>
                    </p>
                </div>
            </div>

            <div class="text-center">
                <p class="text-sm text-gray-500">
                    By signing up, you agree to our 
                    <a href="#" class="text-green-600 hover:text-green-700">Terms of Service</a> 
                    and 
                    <a href="#" class="text-green-600 hover:text-green-700">Privacy Policy</a>
                </p>
            </div>
        </div>
    </main>
<?php include '../includes/footer.php'; ?>