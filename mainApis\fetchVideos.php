 <div id ="videosArea" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
           

            <!-- Video Card 2 -->
        
        
<?php

require_once '../includes/config.php';

//using simple crud fetch videos from db and send in card like interface
if(isset($_POST['channelId']) && isset($_POST['search'])){
    // the search is for the channel where uid is the channel id
    $search = '%' . $_POST['search'] . '%';
    $stmt = $pdo->prepare("SELECT * FROM video WHERE title LIKE :search AND uid = :channelId ORDER BY RAND() LIMIT 24");
    $stmt->bindParam(':channelId', $_POST['channelId'], PDO::PARAM_INT);
    $stmt->bindParam(':search', $search, PDO::PARAM_STR);
    $stmt->execute();
    $videos = $stmt->fetchAll(PDO::FETCH_OBJ);
}
elseif(isset($_POST['search'])){
    $search = '%' . $_POST['search'] . '%';
    $stmt = $pdo->prepare("SELECT * FROM video WHERE title LIKE :search ORDER BY RAND() LIMIT 60");
    $stmt->bindParam(':search', $search, PDO::PARAM_STR);
    $stmt->execute();
    $videos = $stmt->fetchAll(PDO::FETCH_OBJ);
}elseif(isset($_POST['category'])){
    if($_POST['category'] == 0){
        $videos = $db->video->select()    
            ->orderBy('RAND()')
            ->limit(36)
            ->get();
    }
    else{
        $videos = $db->video->select()    
            ->where('category =', $_POST['category'])
            ->orderBy('RAND()')
            ->limit(36)
            ->get();
    }
}elseif(isset($_POST['channelId'])){
        $videos = $db->video->select()    
            ->where('uid =', $_POST['channelId'])
            ->orderBy('RAND()')
            ->limit(16)
            ->get();
    }
else{
    //random videos
    $videos = $db->video->select()    
        ->orderBy('RAND()')
        ->limit(16)
        ->get();

}
if (empty($videos) || $videos == "[]") {
    echo '<!-- No Videos Found Message -->
<div class="text-center py-16 px-4 lg:w-[400%] w-full">
    <div class="max-w-2xl mx-auto">
        <i class="fas fa-video-slash text-gray-300 text-6xl mb-6"></i>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">No Videos Found</h3>
        <p class="text-gray-600 mb-6">
            We couldnt find any Islamic videos matching your search. 
            <br>Try different search or category
        </p>
       
    </div>
</div>';
    exit;
}
foreach ($videos as $video) {
    // Format view count
    $viewCount = $video->views;
    
    if ($viewCount >= 1000000) {
        $formattedViews = number_format($viewCount/1000000, 1) . 'M';
    } else if ($viewCount >= 1000) {
        $formattedViews = number_format($viewCount/1000, 1) . 'K';
    } else {
        $formattedViews = $viewCount;
    }

    // Format upload time
    $uploadDate = $video->upload;
    // Convert string to DateTime object if needed (when using PDO search)
    if (is_string($uploadDate)) {
        $uploadDate = new DateTime($uploadDate);
    }
    $now = new DateTime();
    $interval = $now->diff($uploadDate);
    
    if ($interval->y > 0) {
        $timeAgo = $interval->y . ' year' . ($interval->y > 1 ? 's' : '') . ' ago';
    } elseif ($interval->m > 0) {
        $timeAgo = $interval->m . ' month' . ($interval->m > 1 ? 's' : '') . ' ago';
    } elseif ($interval->d > 6) {
        $weeks = floor($interval->d / 7);
        $timeAgo = $weeks . ' week' . ($weeks > 1 ? 's' : '') . ' ago';
    } elseif ($interval->d > 0) {
        $timeAgo = $interval->d . ' day' . ($interval->d > 1 ? 's' : '') . ' ago';
    }else {
        $timeAgo = 'less than a day ago';
    }
    $user = $db->user[$video->uid];
    ?>
      <div class="bg-white rounded-lg shadow-sm hover:shadow-md transition cursor-pointer" onclick="window.location.href='<?php echo $mainUrl;?>watch?id=<?php echo $video->id;?>'">
                <div class="relative">
                    <img src="<?php echo $video->thumbnailUrl;?>" alt="Video thumbnail" class="w-full h-48 object-cover rounded-t-lg">
                    <div class="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                        <?php echo $video->duration;?>
                    </div>
                </div>
                <div class="p-4">
                    <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2"><?php echo $video->title;?></h3>
                    <p class="text-sm text-gray-600 mb-1"><?php echo $user->name;?></p>
                    <p class="text-sm text-gray-500"><?php echo $formattedViews;?> views • <?php echo $timeAgo;?></p>
                </div>
            </div>
    <?php
}
?>
 <!-- Video Card 1 -->
          
</div>