<?php
include '../../includes/config.php';

$email = $_POST['email'];
$password = $_POST['password'];
//sanitize use phpSanitination Library
$emailGood = filter_var($email, FILTER_VALIDATE_EMAIL);

if($emailGood){
    if($user = $db->user->get(['email' => $email])){
    }else{
        echo "Account not found. Please create an account and try again.";
        exit;
    }
    if ($user) {
        if (password_verify($password, $user->password)) {
            $_SESSION['userEmail'] = $email;
            $_SESSION['userName'] = $user->name;
            $_SESSION['userId'] = $user->id;
            echo "You have logged in successfully. <script>window.location.href='../'</script>";
            exit;
        }else{
            echo "Invalid password. Please try again.";
            exit;
        }
    }
}else{
    echo "Invalid email. Please try again.";
    exit;
}

?>