<?php

include '../../includes/config.php';
if (!isset($_SESSION['userId'])) {
    header("Location: ../../login");
}
$name = $_POST['accountName'];
$bio = $_POST['bio'];
$userId = $_SESSION['userId'];
$contact = $_POST['contact'];
// Handle avatar upload
$avatarPath = '';
if(isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
    $uploadDir = '../../assets/avatars/';
    if (!file_exists($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }
    
    // Delete old avatar if exists and not default
    $currentUser = $db->user[$userId];
    if($currentUser && $currentUser->avatar && $currentUser->avatar !== '/assets/avatar.jpg') {
        $oldAvatarPath = '../../' . ltrim($currentUser->avatar, '/');
        if(file_exists($oldAvatarPath)) {
            unlink($oldAvatarPath);
        }
    }
    
    $fileExtension = pathinfo($_FILES['avatar']['name'], PATHINFO_EXTENSION);
    $fileName = uniqid('avatar_') . '.' . $fileExtension;
    $targetPath = $uploadDir . $fileName;
    
    // Check if file is an actual image
    $check = getimagesize($_FILES['avatar']['tmp_name']);
    if($check !== false) {
        if(move_uploaded_file($_FILES['avatar']['tmp_name'], $targetPath)) {
            $avatarPath = '/assets/avatars/' . $fileName;
        }
    }
}

if(empty($name) || empty($bio) || empty($contact)){
    echo 'Please fill all required fields';
}else{
    $name = $sanitizer->useSanitize($name);
    $bio = $sanitizer->useSanitize($bio);
    $contact = $sanitizer->useSanitize($contact);
    
    // Prepare update data
    $updateData = [
        'name' => $name,
        'description' => $bio,
        'contact' => $contact,
    ];
    
    // Add avatar to update data if uploaded
    if($avatarPath) {
        $updateData['avatar'] = $avatarPath;
    }
    
    //Update a post
    if($db->user[$userId] = $updateData){
        //refresh the page
        echo 'Your information has been updated successfully <script>location.reload()</script>';
    }else{
        echo 'An error occurred while updating your information';
    }
}
?>