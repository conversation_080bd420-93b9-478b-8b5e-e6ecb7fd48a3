{"packages": [{"name": "atlas/pdo", "version": "1.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/atlasphp/Atlas.Pdo.git", "reference": "b64a164290b93bffd2e034dc6e49535afc7dea80"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/atlasphp/Atlas.Pdo/zipball/b64a164290b93bffd2e034dc6e49535afc7dea80", "reference": "b64a164290b93bffd2e034dc6e49535afc7dea80", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"pds/skeleton": "~1.0", "phpunit/phpunit": "~7.0"}, "time": "2020-12-31T16:08:34+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Atlas\\Pdo\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Atlas.Pdo Contributors", "homepage": "https://github.com/atlasphp/Atlas.Pdo/contributors"}], "description": "Provides a PDO instance decorator with convenience methods, and a connection manager.", "homepage": "https://github.com/atlasphp/Atlas.Pdo", "keywords": ["Connection", "database", "pdo", "sql"], "support": {"issues": "https://github.com/atlasphp/Atlas.Pdo/issues", "source": "https://github.com/atlasphp/Atlas.Pdo/tree/1.2.0"}, "install-path": "../atlas/pdo"}, {"name": "atlas/query", "version": "1.3.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/atlasphp/Atlas.Query.git", "reference": "9ba960c3dcac619ab55da8d39c28465db973c900"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/atlasphp/Atlas.Query/zipball/9ba960c3dcac619ab55da8d39c28465db973c900", "reference": "9ba960c3dcac619ab55da8d39c28465db973c900", "shasum": ""}, "require": {"atlas/pdo": "~1.0", "php": ">=7.1"}, "require-dev": {"pds/skeleton": "~1.0", "phpunit/phpunit": "~7.0"}, "time": "2021-10-02T14:53:03+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Atlas\\Query\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Atlas.Query Contributors", "homepage": "https://github.com/atlasphp/Atlas.Query/contributors"}], "description": "Object-oriented query builders for MySQL, Postgres, SQLite, and SQLServer.", "homepage": "https://github.com/atlasphp/Atlas.Query", "keywords": ["database", "db", "delete", "dml", "insert", "mysql", "pdo", "pgsql", "postgres", "postgresql", "query", "select", "sql", "sql server", "sqlite", "sqlserver", "update"], "support": {"issues": "https://github.com/atlasphp/Atlas.Query/issues", "source": "https://github.com/atlasphp/Atlas.Query/tree/1.3.2"}, "install-path": "../atlas/query"}, {"name": "phpsanitization/phpsanitization", "version": "v2.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/farisc0de/PhpSanitization.git", "reference": "a39377ccd2f7dbab4611efcd164e86f85065b3d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/farisc0de/PhpSanitization/zipball/a39377ccd2f7dbab4611efcd164e86f85065b3d1", "reference": "a39377ccd2f7dbab4611efcd164e86f85065b3d1", "shasum": ""}, "require": {"ext-filter": "*", "php": "^7.3 || ^8.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.13.2", "ergebnis/license": "^1.1.0", "ergebnis/php-cs-fixer-config": "~2.13.0", "ergebnis/phpstan-rules": "~0.15.3", "ergebnis/test-util": "^1.4.0", "infection/infection": "~0.18.2", "maglnet/composer-require-checker": "^3.0", "ocramius/package-versions": "*", "phpmd/phpmd": "^2.12", "phpstan/extension-installer": "^1.1.0", "phpstan/phpstan": "~0.12.71", "phpstan/phpstan-deprecation-rules": "~0.12.6", "phpstan/phpstan-phpunit": "~0.12.17", "phpstan/phpstan-strict-rules": "~0.12.9", "phpunit/phpunit": "^9", "psalm/plugin-phpunit": "~0.15.1", "squizlabs/php_codesniffer": "*", "vimeo/psalm": "^4.4.1"}, "time": "2025-05-03T02:29:21+00:00", "type": "library", "extra": {"composer-normalize": {"indent-size": 2, "indent-style": "space"}}, "installation-source": "dist", "autoload": {"psr-4": {"PhpSanitization\\PhpSanitization\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Faris AL-Otaibi", "email": "<EMAIL>"}], "description": "Modern PHP Sanitization Library", "homepage": "https://github.com/farisc0de/PhpSanitization", "keywords": ["filter", "filtering", "input-validation", "php", "php-library", "php-sanitization", "php-sanitize", "php-sanitizer", "php-sanitizer-array", "php8", "sanitization", "sanitize", "sanitizer", "security", "xss-filter"], "support": {"issues": "https://github.com/farisd3v/PhpSanitization/issues", "source": "https://github.com/farisd3v/PhpSanitization"}, "funding": [{"url": "https://github.com/farisc0de", "type": "github"}, {"url": "https://ko-fi.com/fariscode", "type": "ko_fi"}], "install-path": "../phpsanitization/phpsanitization"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "time": "2019-01-08T18:20:26+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "install-path": "../psr/event-dispatcher"}, {"name": "simple-crud/simple-crud", "version": "v7.5.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/oscarotero/simple-crud.git", "reference": "a1d689ad91e1c861cc555bb0aeb71956c7db032c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/oscarotero/simple-crud/zipball/a1d689ad91e1c861cc555bb0aeb71956c7db032c", "reference": "a1d689ad91e1c861cc555bb0aeb71956c7db032c", "shasum": ""}, "require": {"atlas/pdo": "^1.1", "atlas/query": "^1.2", "php": ">=7.2", "psr/event-dispatcher": "^1.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.11", "oscarotero/php-cs-fixer-config": "^1.0", "phpstan/phpstan": "^0.12.84", "phpunit/phpunit": "^8.0|^9.0"}, "time": "2022-04-08T17:25:38+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"SimpleCrud\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://oscarotero.com", "role": "Developer"}], "description": "Create/Read/Update/Delete easily", "homepage": "https://github.com/oscarotero/simple-crud", "keywords": ["crud", "mysql", "sqlite"], "support": {"email": "<EMAIL>", "issues": "https://github.com/oscarotero/simple-crud/issues", "source": "https://github.com/oscarotero/simple-crud/tree/v7.5.3"}, "install-path": "../simple-crud/simple-crud"}], "dev": true, "dev-package-names": []}