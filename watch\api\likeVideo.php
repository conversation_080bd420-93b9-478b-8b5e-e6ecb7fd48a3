<?php

require_once '../../includes/config.php';

//using simple crud fetch videos from db and send in card like interface
$video = $db->video[$_POST['id']];

$newLikes = $video->likes + 1;
//format likes
if ($newLikes >= 1000000) {
    $formattedLikes = number_format($newLikes/1000000, 1) . 'M';
} else if ($newLikes >= 1000) {
    $formattedLikes = number_format($newLikes/1000, 1) . 'K';
} else {
    $formattedLikes = $newLikes;
}

//Update the videos view count
$updatedVideo = $db->video[$_POST['id']] = [
    'likes' => $video->likes + 1
];

if($updatedVideo){
    echo "
    <i class='fas fa-thumbs-up text-green-600'></i>
    <span>$formattedLikes</span>
    ";
}else{
    echo "Error";
}


?>