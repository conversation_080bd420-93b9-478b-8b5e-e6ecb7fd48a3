{"name": "atlas/query", "type": "library", "description": "Object-oriented query builders for MySQL, Postgres, SQLite, and SQLServer.", "keywords": ["mysql", "pdo", "pgsql", "postgres", "postgresql", "sqlite", "sql server", "sqlserver", "query", "select", "insert", "update", "delete", "db", "database", "sql", "dml"], "homepage": "https://github.com/atlasphp/Atlas.Query", "license": "MIT", "authors": [{"name": "Atlas.Query Contributors", "homepage": "https://github.com/atlasphp/Atlas.Query/contributors"}], "require": {"php": ">=7.1", "atlas/pdo": "~1.0"}, "require-dev": {"phpunit/phpunit": "~7.0", "pds/skeleton": "~1.0"}, "autoload": {"psr-4": {"Atlas\\Query\\": "src/"}}, "autoload-dev": {"psr-4": {"Atlas\\Query\\": "tests/"}}}