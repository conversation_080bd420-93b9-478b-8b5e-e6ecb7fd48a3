<?php
require_once '../../includes/config.php';

// Check if user is logged in
if (!isset($_SESSION['userId'])) {
    echo '<div class="bg-white p-6 rounded-lg max-w-md w-full mx-4">
            <h3 class="text-xl font-semibold mb-4">Login Required</h3>
            <p class="text-gray-600 mb-6">Please sign in to report this video.</p>
            <div class="flex justify-end space-x-4">
                <button onclick="closeReportModal()" class="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                <button onclick="window.location.href=\'../login\'" class="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700">Sign In</button>
            </div>
          </div>';
    exit;
}

$userId = $_SESSION['userId'];
$videoId = $_POST['videoId'];
$reason = $_POST['reason'];
$cid = $_POST['cid'];
// Sanitize input
$reason = $sanitizer->useSanitize($reason);

if (empty($reason)) {
    echo '<div class="bg-white p-6 rounded-lg max-w-md w-full mx-4">
            <h3 class="text-xl font-semibold mb-4 text-red-600">Error</h3>
            <p class="text-gray-600 mb-6">Please provide a reason for reporting this video.</p>
            <div class="flex justify-end space-x-4">
                <button onclick="closeReportModal()" class="px-4 py-2 text-gray-600 hover:text-gray-800">Close</button>
            </div>
          </div>';
    exit;
}
//Insert a new post
$insert = $db->report[] = [
    'cid' => $cid,
    'vid' => $videoId,
    'reason' => $reason,
];

if($insert){
    echo '<div class="bg-white p-6 rounded-lg max-w-md w-full mx-4">
            <h3 class="text-xl font-semibold mb-4 text-green-600">Report Submitted</h3>
            <p class="text-gray-600 mb-6">Thank you for your report. We will review it and take appropriate action if necessary.</p>
            <div class="flex justify-end space-x-4">
                <button onclick="closeReportModal()" class="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700">Close</button>
            </div>
          </div>';
}else{
    echo '<div class="bg-white p-6 rounded-lg max-w-md w-full mx-4">
            <h3 class="text-xl font-semibold mb-4 text-red-600">Error</h3>
            <p class="text-gray-600 mb-6">An error occurred while submitting your report. Please try again later.</p>
            <div class="flex justify-end space-x-4">
                <button onclick="closeReportModal()" class="px-4 py-2 text-gray-600 hover:text-gray-800">Close</button>
            </div>
          </div>';
}
?>
