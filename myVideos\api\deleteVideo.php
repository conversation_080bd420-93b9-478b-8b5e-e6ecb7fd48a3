<?php

require_once '../../includes/config.php';

//using simple crud fetch videos from db and send in card like interface
if(!isset($_SESSION['userId'])){
    header("Location: ../../login");
}
if (!isset($_POST['id'])) {
 exit;
}
$videoId = $_POST['id'];

$userId = $_SESSION['userId'];

$video = $db->video[$videoId];
if($video->delete()){
    echo 'Video deleted successfully <script>location.reload()</script>';
}else{
    echo 'An error occurred while deleting the video';
}