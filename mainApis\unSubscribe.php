<?php

require_once '../includes/config.php';

//using simple crud fetch videos from db and send in card like interface
$channelId = $_POST['id'];
if(!isset($_SESSION['userId'])){
    header("Location: ../login");
}
$userId = $_SESSION['userId'];

//check if person already subscribe
if($db->subscriptions->get(['uid' => $userId, 'cid' => $channelId])){
    $subd = $db->subscriptions->get(['uid' => $userId, 'cid' => $channelId]);
    $sub = $db->subscriptions[$subd->id];
    $sub->delete();
    echo '<button class="bg-green-600 text-white px-6 py-2 rounded-full hover:bg-green-700 transition" hx-post="'.$mainUrl.'watch/api/subscribe.php" hx-vals=\'{"cid":"'.$channelId.'"}\' hx-target="this" hx-trigger="click" hx-swap="outerHTML">
            <i class="fas fa-plus mr-1"></i>
            Subscribe
        </button>';
}else{
    echo 'You are not subscribed to this channel';
}
?>