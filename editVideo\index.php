<?php
ini_set('display_errors', 1); 
ini_set('display_startup_errors', 1); 
error_reporting(E_ALL);
require_once '../includes/config.php';
$title = 'Edit Video - Better Muslim';
include '../includes/header.php';
if (!isset($_SESSION['userId'])) {
    header("Location: ../login");
    exit;
}
if (!isset($_GET['id'])) {
    header("Location: ../");
    exit;
}else{
$videoId = $_GET['id'];
}
$video = $db->video[$videoId];

if($video->uid != $_SESSION['userId']){
    header("Location: ../");
    exit;
}
//views
$views = $video->views;
if ($views >= 1000000) {
    $formattedViews = number_format($views/1000000, 1) . 'M';
} else if ($views >= 1000) {
    $formattedViews = number_format($views/1000, 1) . 'K';
} else {
    $formattedViews = $views;
}
//pudished AGO
$uploadDate = $video->upload;
// Convert string to DateTime object if needed (when using PDO search)
if (is_string($uploadDate)) {
    $uploadDate = new DateTime($uploadDate);
}
$now = new DateTime();
$interval = $now->diff($uploadDate);

if ($interval->y > 0) {
    $timeAgo = $interval->y . ' year' . ($interval->y > 1 ? 's' : '') . ' ago';
} elseif ($interval->m > 0) {
    $timeAgo = $interval->m . ' month' . ($interval->m > 1 ? 's' : '') . ' ago';
} elseif ($interval->d > 6) {
    $weeks = floor($interval->d / 7);
    $timeAgo = $weeks . ' week' . ($weeks > 1 ? 's' : '') . ' ago';
} elseif ($interval->d > 0) {
    $timeAgo = $interval->d . ' day' . ($interval->d > 1 ? 's' : '') . ' ago';
}else {
    $timeAgo = 'less than a day ago';
}
//first 32 letters of description
$description = substr($video->description, 0, 124)  . '...';
$currentBeforeVideo = $video->beforeVideoId;
?>


<body class="bg-gray-50">
    
    <?php
    $upload = true;
    include '../includes/navbar.php'; ?>

    <!-- Main Content -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Enhanced Update Form -->
        <div id="update-output" class="bg-white rounded-xl shadow-lg overflow-hidden">
            <!-- Form Header -->
            <div class="bg-gradient-to-r from-blue-600 to-green-600 px-8 py-6">
                <div class="flex items-center space-x-3">
                    <div class="bg-white bg-opacity-20 rounded-full p-3">
                        <i class="fas fa-edit text-white text-2xl"></i>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-white">Update Video</h2>
                        <p class="text-blue-100">Edit your video details and settings</p>
                    </div>
                </div>
            </div>

            <!-- Current Video Preview -->
            <div class="bg-gray-50 px-8 py-6 border-b">
                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-eye text-blue-600 mr-2"></i>
                    Current Video Preview
                </h3>
                <div class="flex items-start space-x-4">
                    <img src="<?php echo $video->thumbnailUrl;?>" alt="Current Thumbnail" 
                         class="w-32 h-20 object-cover rounded-lg border-2 border-gray-200">
                    <div class="flex-1">
                        <h4 class="font-semibold text-gray-900 mb-1"><?php echo $video->title;?></h4>
                        <p class="text-sm text-gray-600 mb-2"><?php echo $description;?></p>
                        <div class="flex items-center space-x-4 text-sm text-gray-500">
                            <span class="flex items-center">
                                <i class="fas fa-eye mr-1"></i>
                                <?php echo $formattedViews;?>   views
                            </span>
                         
                            <span class="flex items-center">
                                <i class="fas fa-calendar mr-1"></i>
                                Published <?php echo $timeAgo;?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Content -->
            <div class="p-8">
                <form class="space-y-8" hx-post="api/updateVideo.php" hx-vals='{"videoId":"<?php echo $videoId;?>"}' hx-target="#update-output" hx-swap="innerHTML">
                    <!-- Hidden field for video ID -->

                    <!-- Video Details Section -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <i class="fas fa-video text-green-600 mr-2"></i>
                            Video Information
                        </h3>
                        
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Video URL -->
                            <!-- <div class="lg:col-span-2">
                                <label for="videoUrl" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-link text-green-600 mr-1"></i>
                                    Video URL *
                                </label>
                                <div class="relative">
                                    <input type="url" id="videoUrl" name="videoUrl" required
                                           value="<?php echo $video->videoUrl;?>"
                                           class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                                           placeholder="https://example.com/video.mp4">
                                    <i class="fas fa-globe absolute left-3 top-3.5 text-gray-400"></i>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">Update the direct URL to your video file</p>
                            </div> -->

                            <!-- Thumbnail URL -->
                            <!-- <div class="lg:col-span-2">
                                <label for="thumbnailUrl" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-image text-green-600 mr-1"></i>
                                    Thumbnail URL *
                                </label>
                                <div class="relative">
                                    <input type="url" id="thumbnailUrl" name="thumbnailUrl" required
                                           value="<?php echo $video->thumbnailUrl;?>"
                                           class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                                           placeholder="https://example.com/thumbnail.jpg">
                                    <i class="fas fa-camera absolute left-3 top-3.5 text-gray-400"></i>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">Recommended size: 1280x720 pixels (16:9 ratio)</p>
                            </div>
                             -->
                            <div class="lg:col-span-2">
                                <label for="duration" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-clock text-green-600 mr-1"></i>
                                    Duration *
                                </label>
                                <div class="relative">
                                    <input type="text" id="duration" name="duration" required
                                           value="<?php echo $video->duration;?>"
                                           class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors"
                                           placeholder="1:15:30">
                                    <i class="fas fa-clock absolute left-3 top-3.5 text-gray-400"></i>                                
                                </div>
                                <p class="text-xs text-gray-500 mt-1">In this format 15:30.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Content Details Section -->
                    <div class="bg-blue-50 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <i class="fas fa-edit text-blue-600 mr-2"></i>
                            Content Details
                        </h3>
                        
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Title -->
                            <div class="lg:col-span-2">
                                <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-heading text-blue-600 mr-1"></i>
                                    Video Title *
                                </label>
                                <input type="text" id="title" name="title" required
                                       value="<?php echo $video->title;?>"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                                       placeholder="Enter an engaging title for your video"
                                       maxlength="130">
                                <div class="flex justify-between mt-1">
                                    <p class="text-xs text-gray-500">Make it descriptive and engaging</p>
                                    <span class="text-xs text-gray-400" id="title-counter">0/130</span>
                                </div>
                            </div>

                            <!-- Category -->
                            <div>
                                <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-tags text-blue-600 mr-1"></i>
                                    Category *
                                </label>
                                <div class="relative">
                                    <select id="category" name="category" required
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors appearance-none bg-white">
                                        <option value="">Choose a category</option>
                                        <option value="1" <?php echo $video->category == 1 ? 'selected' : '';?>>📚 Ilm (Islamic Knowledge)</option>
                                        <option value="2" <?php echo $video->category == 2 ? 'selected' : '';?>>💪 Fitness</option>
                                        <option value="3" <?php echo $video->category == 3 ? 'selected' : '';?>>🛠️ Skills</option>
                                    </select>
                                    <i class="fas fa-chevron-down absolute right-3 top-3.5 text-gray-400 pointer-events-none"></i>
                                </div>
                            </div>

                            <!-- Description -->
                            <div class="lg:col-span-2">
                                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-align-left text-blue-600 mr-1"></i>
                                    Description
                                </label>
                                <textarea id="description" name="description" rows="4"
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none transition-colors"
                                          placeholder="Describe your video content... What will viewers learn? Include relevant Islamic context if applicable."
                                          maxlength="2000"><?php echo $video->description;?></textarea>
                                <div class="flex justify-between mt-1">
                                    <p class="text-xs text-gray-500">Provide context and key points covered in your video</p>
                                    <span class="text-xs text-gray-400" id="desc-counter">0/2000</span>
                                </div>
                            </div>
                        </div>
                    </div>

                     <!-- Playlist Search Section -->
                        <div class="bg-indigo-50 rounded-lg p-6 ">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <i class="fas fa-list-ul text-indigo-600 mr-2"></i>
                                Video Before This One In a Series
                            </h3>
                            
                            <!-- Search Bar -->
                            <div class="mb-6">
                                <div class="relative">
                                    <input type="text" id="playlistSearch" 
                                        class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                                        placeholder="Search your videos to add to playlist..."
                                        hx-post="<?php echo $mainUrl;?>mainApis/fetchBeforeVideos.php"
                                        hx-vals='{"currentBeforeVideo":"<?php echo $currentBeforeVideo;?>"}'
                                        hx-target="#videoGrid"
                                        hx-trigger="keyup changed delay:500ms"
                                        hx-swap="innerHTML"
                                        name="search"
                                        >
                                    <i class="fas fa-search absolute left-3 top-3.5 text-gray-400"></i>
                                </div>
                                <div class="flex gap-10" >
                                    <div>
                                    <p class="text-xs text-gray-500 mt-2">Find and select a video that comes before this one from your channel</p>
                                    </div>
                                    <div class="mt-1">
                                        <?php
                                        if($currentBeforeVideo == 0){
                                            $checked = "checked";
                                        }
                                        else{
                                            $checked= " ";
                                        }
                                        ?>
                                    <input type="checkbox" <?php echo $checked;?> class="video-checkbox" name="beforeVideo" value="0"><label class="inline-block px-2 py-0.5 bg-red-100 ml-1 text-red-800 text-xs rounded-full">This video is not part of a series</label>
                                    </div>
                                </div>
                            </div>

                        

                            <!-- Video Grid -->
                            <div class="max-h-96 overflow-y-auto border border-gray-200 rounded-lg">
                                <div id="videoGrid" hx-post="<?php echo $mainUrl;?>mainApis/fetchBeforeVideos.php" hx-vals='{"currentBeforeVideo":"<?php echo $currentBeforeVideo;?>"}' hx-target="#videoGrid" hx-trigger="load" hx-swap="innerHTML" class="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
                                    
                               


                                    <!-- No Results Message -->
                                    
                                </div>
                            </div>

                            <!-- Selected Videos Summary -->
                            <div id="selectedSummary" class="hidden mt-4 p-4 bg-indigo-100 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <i class="fas fa-check-circle text-indigo-600 mr-2"></i>
                                        <span class="font-medium text-indigo-900">
                                            <span id="selectedCount">0</span> video(s) selected for playlist
                                        </span>
                                    </div>
                                    <button onclick="clearSelection()" class="text-indigo-600 hover:text-indigo-800 text-sm">
                                        <i class="fas fa-times mr-1"></i>
                                        Clear Selection
                                    </button>
                                </div>
                                <div id="selectedVideosList" class="mt-2 text-sm text-indigo-700"></div>
                            </div>
                        </div>
                    <!-- Islamic Content Guidelines -->
                    <div class="bg-green-50 border-l-4 border-green-400 p-6 rounded-r-lg">
                        <div class="flex items-start">
                            <i class="fas fa-mosque text-green-600 text-xl mt-1 mr-3"></i>
                            <div>
                                <h4 class="text-sm font-semibold text-green-800 mb-2">Islamic Content Guidelines</h4>
                                <ul class="text-sm text-green-700 space-y-1">
                                    <li class="flex items-start">
                                        <i class="fas fa-check-circle text-green-600 mr-2 mt-0.5 text-xs"></i>
                                        Ensure content remains beneficial and aligned with Islamic teachings
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-check-circle text-green-600 mr-2 mt-0.5 text-xs"></i>
                                        Verify authenticity of any religious information shared
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-check-circle text-green-600 mr-2 mt-0.5 text-xs"></i>
                                        Maintain proper Islamic etiquette and respect
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-check-circle text-green-600 mr-2 mt-0.5 text-xs"></i>
                                        Consider the impact of changes on your audience
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row justify-between items-center pt-6 border-t border-gray-200 space-y-4 sm:space-y-0">
                        <div class="flex items-center space-x-2 text-sm text-gray-600">
                            <i class="fas fa-info-circle text-blue-500"></i>
                            <span>Changes will be saved immediately</span>
                        </div>
                        
                        <div class="flex space-x-4">
                           
                            <button type="submit" class="bg-gradient-to-r from-green-600 to-blue-600 text-white px-8 py-3 rounded-lg hover:from-green-700 hover:to-blue-700 transition-all duration-200 font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                                <i class="fas fa-save mr-2"></i>
                                Update Video
                            </button>
                        </div>
                    </div>
                </form>

                <!-- Update Output -->
               
            </div>
        </div>

      
    </main>

    

    <!-- JavaScript for Character Counters and Form Interactions -->
    <script>
           // Ensure only one checkbox can be selected at a time
                document.addEventListener('change', function(e) {
                    if (e.target.classList.contains('video-checkbox')) {
                        const checkboxes = document.querySelectorAll('.video-checkbox');
                        checkboxes.forEach(checkbox => {
                            if (checkbox !== e.target) {
                                checkbox.checked = false;
                            }
                        });
                    }
                });
    document.addEventListener('DOMContentLoaded', function() {
        // Title character counter
        const titleInput = document.getElementById('title');
        const titleCounter = document.getElementById('title-counter');
        
        function updateTitleCounter() {
            const length = titleInput.value.length;
            titleCounter.textContent = `${length}/130`;
            titleCounter.className = length > 120 ? 'text-xs text-red-500' : 'text-xs text-gray-400';
        }
        
        titleInput.addEventListener('input', updateTitleCounter);
        updateTitleCounter(); // Initialize counter
        
        // Description character counter
        const descInput = document.getElementById('description');
        const descCounter = document.getElementById('desc-counter');
        
        function updateDescCounter() {
            const length = descInput.value.length;
            descCounter.textContent = `${length}/2000`;
            descCounter.className = length > 1950 ? 'text-xs text-red-500' : 'text-xs text-gray-400';
        }
        
        descInput.addEventListener('input', updateDescCounter);
        updateDescCounter(); // Initialize counter

    });

    //     // Form change detection
    //     const form = document.querySelector('form');
    //     const originalData = new FormData(form);
    //     let hasChanges = false;

    //     function detectChanges() {
    //         const currentData = new FormData(form);
    //         hasChanges = false;
            
    //         for (let [key, value] of currentData.entries()) {
    //             if (originalData.get(key) !== value) {
    //                 hasChanges = true;
    //                 break;
    //             }
    //         }
            
    //         // Update button text if changes detected
    //         const submitBtn = form.querySelector('button[type="submit"]');
    //         if (hasChanges) {
    //             submitBtn.innerHTML = '<i class="fas fa-save mr-2"></i>Save Changes';
    //             submitBtn.classList.add('animate-pulse');
    //         } else {
    //             submitBtn.innerHTML = '<i class="fas fa-save mr-2"></i>Update Video';
    //             submitBtn.classList.remove('animate-pulse');
    //         }
    //     }

    //     // Add change listeners to form elements
    //     form.addEventListener('input', detectChanges);
    //     form.addEventListener('change', detectChanges);

    //     // Warn before leaving if there are unsaved changes
    //     window.addEventListener('beforeunload', function(e) {
    //         if (hasChanges) {
    //             e.preventDefault();
    //             e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
    //             return e.returnValue;
    //         }
    //     });

    //     // Preview changes functionality
    //     const previewBtn = form.querySelector('button[type="button"]');
    //     previewBtn.addEventListener('click', function() {
    //         // Create preview modal or redirect to preview page
    //         alert('Preview functionality would show how the updated video will look to viewers.');
    //     });
    // });

    // // Auto-save functionality (optional)
    // let autoSaveTimeout;
    // function autoSave() {
    //     clearTimeout(autoSaveTimeout);
    //     autoSaveTimeout = setTimeout(() => {
    //         // Auto-save logic here
    //         console.log('Auto-saving changes...');
    //         showNotification('Changes auto-saved', 'info');
    //     }, 30000); // Auto-save after 30 seconds of inactivity
    // }

    // // Show notification function
    // function showNotification(message, type = 'info') {
    //     const notification = document.createElement('div');
    //     notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 ${
    //         type === 'success' ? 'bg-green-500 text-white' : 
    //         type === 'error' ? 'bg-red-500 text-white' : 
    //         'bg-blue-500 text-white'
    //     }`;
    //     notification.innerHTML = `
    //         <div class="flex items-center">
    //             <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} mr-2"></i>
    //             <span>${message}</span>
    //         </div>
    //     `;
        
    //     document.body.appendChild(notification);
        
    //     setTimeout(() => {
    //         notification.style.opacity = '0';
    //         notification.style.transform = 'translateX(100%)';
    //         setTimeout(() => notification.remove(), 300);
    //     }, 3000);
    // }
    </script>
<?php include '../includes/footer.php'; ?>