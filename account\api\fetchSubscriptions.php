
<div id="subscriptions-list" class="space-y-6">

<?php 
include '../../includes/config.php';
if(!isset($_SESSION['userId'])){
    header("Location: ../login");
}
$userId = $_SESSION['userId'];

$subscriptions = $db->subscriptions->select()    
    ->where('uid =', $userId)
    ->get();
if (empty($subscriptions) || $subscriptions == "[]") {
    echo '
    <div class="text-center py-16 px-4 lg:w-[400%] w-full">
        <div class="max-w-2xl mx-auto">
            <i class="fas fa-heart-broken text-gray-300 text-6xl mb-6"></i>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">No Subscriptions Found</h3>
            <p class="text-gray-600 mb-6">
                You havent subscribed to any Islamic channels yet. 
                <br>Discover beneficial content from the Ummah!
            </p>
           
        </div>
    </div>';
    exit;
}
foreach ($subscriptions as $subscription) {
    $channel = $db->user[$subscription->cid];
    $videosNumber = $db->video
    ->selectAggregate('COUNT')
    ->where('uid =', $subscription->cid)
    ->get();
    //format subscribers
$subscribers = $db->subscriptions
    ->selectAggregate('COUNT')
    ->where('cid =', $subscription->cid)
    ->get();
    if ($subscribers >= 1000000) {
        $formattedSubscribers = number_format($subscribers/1000000, 1) . 'M';
    } else if ($subscribers >= 1000) {
        $formattedSubscribers = number_format($subscribers/1000, 1) . 'K';
    } else {
        $formattedSubscribers = $subscribers;
    }
    //Get last video uplaoded date
   $lastVideo = $db->video->getOrCreate(['uid' => $subscription->cid]);
    $lastVideoDate = $lastVideo->upload;
    // Convert string to DateTime object if needed (when using PDO search)
    if (is_string($lastVideoDate)) {
        $lastVideoDate = new DateTime($lastVideoDate);
    }
  
    $now = new DateTime();
    $interval = $now->diff($lastVideoDate);
    if ($interval->y > 0) {
        $timeAgo = $interval->y . ' year' . ($interval->y > 1 ? 's' : '') . ' ago';
    } elseif ($interval->m > 0) {
        $timeAgo = $interval->m . ' month' . ($interval->m > 1 ? 's' : '') . ' ago';
    } elseif ($interval->d > 1) {   
        $timeAgo = $interval->d . ' days ago';
    } else {
        $timeAgo = 'less than a day ago';
    }
    //make subscription ->date a string and format first
    $subbedDate = $subscription->date;
    if (is_string($subbedDate)) {
        $subbedDate = new DateTime($subbedDate);
    }
    $now = new DateTime();
    $subbedAgo = $now->diff($subbedDate);
    if ($subbedAgo->y > 0) {
        $subbedAgo = $subbedAgo->y . ' year' . ($interval->y > 1 ? 's' : '') . ' ago';
    } elseif ($subbedAgo->m > 0) {
        $subbedAgo = $subbedAgo->m . ' month' . ($interval->m > 1 ? 's' : '') . ' ago';
    } elseif ($subbedAgo->d > 1) {
        $subbedAgo = $subbedAgo->d . ' days ago';
    } else {
        $subbedAgo = 'less than a day ago';
    }
?>
                                    <div class="subscription-item flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition" data-channel="sheikh-ahmad">
                                        <div class="flex items-center space-x-4">
                                            <img src="<?php echo $mainUrl;?><?php echo $channel->avatar;?>" alt="<?php echo $channel->name;?>"
                                                class="w-14 h-14 rounded-full object-cover">
                                            <div class="flex-1">
                                                <div class="flex items-center space-x-2 mb-1">
                                                    <h3 class="font-semibold text-gray-900"><?php echo $channel->name;?></h3>
                                                </div>
                                                <p class="text-sm text-gray-600 mb-1"><?php echo $formattedSubscribers;?> subscribers • <?php echo $videosNumber;?> videos</p>
                                                <p class="text-xs text-gray-500">Subscribed <?php echo $subbedAgo;?></p>
                                                <div class="flex items-center space-x-4 mt-2">
                                                  
                                                    <span class="text-xs text-gray-500">Last video: <?php echo $timeAgo;?></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-3">
                                            <a href="<?php echo $mainUrl;?>channel?id=<?php echo $channel->id;?>" class="text-green-600 hover:text-green-700 text-sm font-medium" target="_blank">
                                                <i class="fas fa-external-link-alt mr-1"></i>
                                                Visit Channel
                                            </a>
                                            <div class="relative">
                                           
                                            </div>
                                            <button class="unsubscribe-btn bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition text-sm font-medium" 
                                                   hx-post="<?php echo $mainUrl;?>mainApis/unSubscribe.php" hx-vals='{"id":"<?php echo $channel->id;?>"}' hx-target="this" hx-trigger="click" hx-swap="innerHTML">
                                                <i class="fas fa-user-minus mr-1"></i>
                                                Unsubscribe
                                            </button>
                                        </div>
                                    </div>

                                   

                                
<?php
}
?>
</div>