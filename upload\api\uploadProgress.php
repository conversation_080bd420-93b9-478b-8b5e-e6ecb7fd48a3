<?php
include '../../includes/config.php';
if (!isset($_SESSION['userId'])) {
    header("Location: ../../login");
    exit;
}

// Set content type for Server-Sent Events
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Connection: keep-alive');

// Function to send progress update
function sendProgress($step, $percentage, $message) {
    echo "data: " . json_encode([
        'step' => $step,
        'percentage' => $percentage,
        'message' => $message,
        'timestamp' => time()
    ]) . "\n\n";
    ob_flush();
    flush();
}

// Function to send error
function sendError($error) {
    echo "data: " . json_encode([
        'error' => true,
        'message' => $error,
        'timestamp' => time()
    ]) . "\n\n";
    ob_flush();
    flush();
}

// Function to send success
function sendSuccess($videoUrl, $thumbnailUrl, $server) {
    echo "data: " . json_encode([
        'success' => true,
        'videoUrl' => $videoUrl,
        'thumbnailUrl' => $thumbnailUrl,
        'server' => $server,
        'timestamp' => time()
    ]) . "\n\n";
    ob_flush();
    flush();
}

// Start progress tracking
sendProgress('validation', 10, 'Validating files...');
sleep(1);

// Check if this is a progress request
if (isset($_GET['action']) && $_GET['action'] === 'progress') {
    // This would be called via JavaScript to track progress
    // For now, we'll simulate progress steps
    
    sendProgress('validation', 20, 'Files validated successfully');
    sleep(1);
    
    sendProgress('upload', 30, 'Starting upload to server...');
    sleep(2);
    
    sendProgress('upload', 50, 'Uploading video file...');
    sleep(3);
    
    sendProgress('upload', 70, 'Uploading thumbnail...');
    sleep(2);
    
    sendProgress('upload', 90, 'Processing on server...');
    sleep(1);
    
    sendProgress('complete', 100, 'Upload completed successfully!');
    
    // Close the connection
    echo "data: {\"close\": true}\n\n";
    ob_flush();
    flush();
}
?>
