<?php
 
include '../../includes/config.php';
if (!isset($_SESSION['userId'])) {
    header("Location: ../../login");
}
$userId = $_SESSION['userId'];

// Get pagination parameters
$page = isset($_POST['page']) ? (int)$_POST['page'] : 1;
$perPage = 5; // Videos per page
$searchTerm = isset($_POST['search']) ? $_POST['search'] : '';

if(!empty($searchTerm)){
    $search = '%' . $searchTerm . '%';
    // For search, we'll use PDO directly but still implement pagination
    $offset = ($page - 1) * $perPage;

    // Get total count for search
    $countStmt = $pdo->prepare("SELECT COUNT(*) as total FROM video WHERE title LIKE :search AND uid = :userId");
    $countStmt->bindParam(':userId', $userId, PDO::PARAM_INT);
    $countStmt->bindParam(':search', $search, PDO::PARAM_STR);
    $countStmt->execute();
    $totalRows = $countStmt->fetch(PDO::FETCH_OBJ)->total;

    // Get paginated search results
    $stmt = $pdo->prepare("SELECT * FROM video WHERE title LIKE :search AND uid = :userId ORDER BY id ASC LIMIT :limit OFFSET :offset");
    $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
    $stmt->bindParam(':search', $search, PDO::PARAM_STR);
    $stmt->bindParam(':limit', $perPage, PDO::PARAM_INT);
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();
    $videos = $stmt->fetchAll(PDO::FETCH_OBJ);

    // Calculate pagination info for search
    $totalPages = ceil($totalRows / $perPage);
    $pagination = [
        'totalRows' => $totalRows,
        'totalPages' => $totalPages,
        'currentPage' => $page,
        'previousPage' => $page > 1 ? $page - 1 : null,
        'nextPage' => $page < $totalPages ? $page + 1 : null,
        'perPage' => $perPage
    ];
}else{
    // Using simple crud with pagination
    $query = $db->video->select()
            ->where('uid =', $userId)
            ->orderBy('id ASC')
            ->page($page)
            ->perPage($perPage);

    $videos = $query->get();
    $pagination = $query->getPageInfo();

    // Add perPage to pagination info since simple-crud doesn't include it
    $pagination['perPage'] = $perPage;
}
if (empty($videos) || $videos == "[]") {
    echo '<!-- No Videos Found Message -->
<div class="text-center py-16 px-4  w-full">
    <div class="max-w-2xl mx-auto">
        <i class="fas fa-video-slash text-gray-300 text-6xl mb-6"></i>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">No Videos Found</h3>
        <p class="text-gray-600 mb-6">
            We couldnt find any videos matching your search.
            <br>Try different search keyword
        </p>

    </div>
</div>';
    exit;
}

// Start output buffering to capture video list HTML
ob_start();
        foreach ($videos as $video) {
            // Format view count
            $viewCount = $video->views;
            
            if ($viewCount >= 1000000) {
                $formattedViews = number_format($viewCount/1000000, 1) . 'M';
            } else if ($viewCount >= 1000) {
                $formattedViews = number_format($viewCount/1000, 1) . 'K';
            } else {
                $formattedViews = $viewCount;
            }

            // Format upload time
            $uploadDate = $video->upload;
            // Convert string to DateTime object if needed (when using PDO search)
            if (is_string($uploadDate)) {
                $uploadDate = new DateTime($uploadDate);
            }
            $now = new DateTime();
            $interval = $now->diff($uploadDate);
            
            if ($interval->y > 0) {
                $timeAgo = $interval->y . ' year' . ($interval->y > 1 ? 's' : '') . ' ago';
            } elseif ($interval->m > 0) {
                $timeAgo = $interval->m . ' month' . ($interval->m > 1 ? 's' : '') . ' ago';
            } elseif ($interval->d > 6) {
                $weeks = floor($interval->d / 7);
                $timeAgo = $weeks . ' week' . ($weeks > 1 ? 's' : '') . ' ago';
            } elseif ($interval->d > 0) {
                $timeAgo = $interval->d . ' day' . ($interval->d > 1 ? 's' : '') . ' ago';
            }else {
                $timeAgo = 'less than a day ago';
            }
            
            $videoId = $video->id;

?>
        <div class="p-6 hover:bg-gray-50">
                    <div class="flex items-center space-x-4">
                        <img src="<?php echo $video->thumbnailUrl;?>" alt="Video thumbnail" class="w-20 h-14 object-cover rounded">
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 mb-1"><?php echo $video->title;?></h3>
                            <p class="text-sm text-gray-600 mb-2">A beautiful recitation of the opening chapter of the Holy Quran...</p>
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span class="flex items-center">
                                    <i class="fas fa-eye mr-1"></i>
                                    <?php echo $formattedViews;?> views
                                </span>
                               
                               
                                <span>Published <?php echo $timeAgo;?></span>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
    <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Published</span>
    <div class="relative">
        <button class="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition" 
                onclick="toggleDropdown(this)">
            <i class="fas fa-ellipsis-v"></i>
        </button>
        
        <!-- Islamic-themed Dropdown Menu -->
        <div class="dropdown-menu hidden absolute right-0 mt-2 w-52 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
            <div class="py-2">
                <!-- Edit Video -->
                <a href="<?php echo $mainUrl;?>editVideo?id=<?php echo $videoId;?>"><button class="w-full text-left px-4 py-2 text-sm text-green-600 hover:bg-green-50 flex items-center transition">
                    <i class="fas fa-edit text-green-600 mr-3"></i>
                    Edit Video Details
                </a>
                <!-- Delete Video -->
                <button class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center transition" 
                        hx-post="api/deleteVideo.php" hx-vals='{"id":"<?php echo $videoId;?>"}' hx-target="this" hx-trigger="click" hx-swap="innerHTML">
                    <i class="fas fa-trash text-red-600 mr-3"></i>
                    Delete Video
                </button>
            </div>
        </div>
        </div>
        </div>
<script>
/// Toggle dropdown menu
function toggleDropdown(button) {
    const dropdown = button.nextElementSibling;
    const allDropdowns = document.querySelectorAll('.dropdown-menu');
    
    // Close all other dropdowns
    allDropdowns.forEach(menu => {
        if (menu !== dropdown) {
            menu.classList.add('hidden');
        }
    });
    
    // Toggle current dropdown
    dropdown.classList.toggle('hidden');
}

// Close dropdown when clicking outside
document.addEventListener('click', function(e) {
    if (!e.target.closest('.relative')) {
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            menu.classList.add('hidden');
        });
    }
});

// Close dropdown when pressing Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            menu.classList.add('hidden');
        });
    }
});
</script>
                    </div>
                </div>
<?php
        }

// Get the video list HTML
$videoListHtml = ob_get_clean();

// Calculate pagination display info
$startItem = ($pagination['currentPage'] - 1) * $pagination['perPage'] + 1;
$endItem = min($pagination['currentPage'] * $pagination['perPage'], $pagination['totalRows']);

?>

<!-- Videos List -->
<?php echo $videoListHtml; ?>

<!-- Pagination -->
<div class="px-6 py-4 border-t bg-gray-50">
    <div class="flex items-center justify-between">
        <p class="text-sm text-gray-700">
            Showing <span class="font-medium"><?php echo $startItem; ?></span> to
            <span class="font-medium"><?php echo $endItem; ?></span> of
            <span class="font-medium"><?php echo $pagination['totalRows']; ?></span> videos
        </p>
        <div class="flex items-center space-x-2">
            <?php if ($pagination['previousPage']): ?>
                <button
                    hx-post="api/getVideos.php"
                    hx-target="#videosList"
                    hx-swap="innerHTML"
                    hx-vals='{"page": <?php echo $pagination['previousPage']; ?><?php echo !empty($searchTerm) ? ', "search": "' . htmlspecialchars($searchTerm) . '"' : ''; ?>}'
                    class="px-3 py-2 text-sm text-gray-700 hover:text-gray-900">
                    Previous
                </button>
            <?php else: ?>
                <button class="px-3 py-2 text-sm text-gray-500 disabled:opacity-50" disabled>
                    Previous
                </button>
            <?php endif; ?>

            <?php
            // Show page numbers
            $startPage = max(1, $pagination['currentPage'] - 2);
            $endPage = min($pagination['totalPages'], $pagination['currentPage'] + 2);

            for ($i = $startPage; $i <= $endPage; $i++): ?>
                <?php if ($i == $pagination['currentPage']): ?>
                    <button class="px-3 py-2 text-sm bg-green-600 text-white rounded"><?php echo $i; ?></button>
                <?php else: ?>
                    <button
                        hx-post="api/getVideos.php"
                        hx-target="#videosList"
                        hx-swap="innerHTML"
                        hx-vals='{"page": <?php echo $i; ?><?php echo !empty($searchTerm) ? ', "search": "' . htmlspecialchars($searchTerm) . '"' : ''; ?>}'
                        class="px-3 py-2 text-sm text-gray-700 hover:text-gray-900">
                        <?php echo $i; ?>
                    </button>
                <?php endif; ?>
            <?php endfor; ?>

            <?php if ($pagination['nextPage']): ?>
                <button
                    hx-post="api/getVideos.php"
                    hx-target="#videosList"
                    hx-swap="innerHTML"
                    hx-vals='{"page": <?php echo $pagination['nextPage']; ?><?php echo !empty($searchTerm) ? ', "search": "' . htmlspecialchars($searchTerm) . '"' : ''; ?>}'
                    class="px-3 py-2 text-sm text-gray-700 hover:text-gray-900">
                    Next
                </button>
            <?php else: ?>
                <button class="px-3 py-2 text-sm text-gray-500 disabled:opacity-50" disabled>
                    Next
                </button>
            <?php endif; ?>
        </div>
    </div>
</div>