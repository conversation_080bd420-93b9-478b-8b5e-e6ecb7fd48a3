{"name": "phpsanitization/phpsanitization", "type": "library", "description": "Modern PHP Sanitization Library", "keywords": ["sanitization", "filter", "filtering", "php", "php8", "php-library", "sanitizer", "xss-filter", "sanitize", "php-sanitizer", "php-sanitizer-array", "php-sanitize", "php-sanitization", "input-validation", "security"], "homepage": "https://github.com/farisc0de/PhpSanitization", "license": "MIT", "authors": [{"name": "Faris AL-Otaibi", "email": "<EMAIL>"}], "require": {"php": "^7.3 || ^8.0", "ext-filter": "*"}, "require-dev": {"ergebnis/composer-normalize": "^2.13.2", "ergebnis/license": "^1.1.0", "ergebnis/php-cs-fixer-config": "~2.13.0", "ergebnis/phpstan-rules": "~0.15.3", "ergebnis/test-util": "^1.4.0", "infection/infection": "~0.18.2", "maglnet/composer-require-checker": "^3.0", "ocramius/package-versions": "*", "phpstan/extension-installer": "^1.1.0", "phpstan/phpstan": "~0.12.71", "phpstan/phpstan-deprecation-rules": "~0.12.6", "phpstan/phpstan-phpunit": "~0.12.17", "phpstan/phpstan-strict-rules": "~0.12.9", "phpunit/phpunit": "^9", "psalm/plugin-phpunit": "~0.15.1", "squizlabs/php_codesniffer": "*", "vimeo/psalm": "^4.4.1", "phpmd/phpmd": "^2.12"}, "extra": {"composer-normalize": {"indent-size": 2, "indent-style": "space"}}, "autoload": {"psr-4": {"PhpSanitization\\PhpSanitization\\": "src/"}}, "autoload-dev": {"psr-4": {"PhpSanitization\\PhpSanitization\\Test\\": "test/"}}, "scripts": {"cs-check": "vendor/bin/phpcs", "cs-fix": "vendor/bin/phpcbf", "test": "vendor/bin/phpunit", "fix-mess": "vendor/bin/phpmd src/Sanitization.php ansi cleancode"}, "support": {"issues": "https://github.com/farisd3v/PhpSanitization/issues", "source": "https://github.com/farisd3v/PhpSanitization"}, "config": {"allow-plugins": {"composer/package-versions-deprecated": true, "infection/extension-installer": true, "ergebnis/composer-normalize": true, "phpstan/extension-installer": true}}}