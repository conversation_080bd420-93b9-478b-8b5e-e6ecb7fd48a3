  <?php

  require_once '../../includes/config.php';

  //using simple crud fetch videos from db and send in card like interface
 $videoId = $_POST['id'];
 $currentVideo = $db->video[$_POST['id']];

 // Function to get the entire video chain
 function getVideoChain($db, $currentVideoId) {
     $videos = [];
     $processedIds = []; // To prevent infinite loops

     // Get the current video object
     $currentVideoObj = $db->video[$currentVideoId];

     // First, find all videos that come before the current video
     $beforeVideoId = $currentVideoObj->beforeVideoId;
     while ($beforeVideoId && $beforeVideoId != 0 && !in_array($beforeVideoId, $processedIds)) {
         $processedIds[] = $beforeVideoId;
         $beforeVideo = $db->video[$beforeVideoId];

         if ($beforeVideo) {
             // Add to the beginning of the array to maintain order
             array_unshift($videos, $beforeVideo);
             $beforeVideoId = $beforeVideo->beforeVideoId;
         } else {
             break;
         }
     }

     // Add the current video to the chain at its proper position
     $videos[] = $currentVideoObj;
     $processedIds[] = $currentVideoId;

     // Then, find all videos that come after the current video
     $nextVideoId = $currentVideoId;
     while (true) {
         // Find videos that have this video as their beforeVideoId
         $nextVideos = $db->video->select()
             ->where('beforeVideoId =', $nextVideoId)
             ->get();

         if ($nextVideos && $nextVideos != "[]") {
             $foundNext = false;
             foreach ($nextVideos as $nextVideo) {
                 if (!in_array($nextVideo->id, $processedIds)) {
                     $videos[] = $nextVideo;
                     $processedIds[] = $nextVideo->id;
                     $nextVideoId = $nextVideo->id;
                     $foundNext = true;
                     break; // Only follow one chain path
                 }
             }
             // If we didn't find any unprocessed videos, break
             if (!$foundNext) {
                 break;
             }
         } else {
             break;
         }
     }

     return $videos;
 }

 // Get the entire video chain
 $videos = getVideoChain($db, $videoId);
 // Check if we have videos in the chain (should always have at least the current video)
 if(!empty($videos) && $videos != "[]"){
     ?>
     <div class="bg-white rounded-lg p-4 mb-6">
         <h3 class="font-semibold mb-4">Video Series (<?php echo count($videos); ?> videos)</h3>
         <div class="space-y-3">
             <?php
foreach ($videos as $index => $chainVideo) {
     $channel = $db->user[$chainVideo->uid];

     // Format view count
     $viewCount = $chainVideo->views;
     if ($viewCount >= 1000000) {
         $formattedViews = number_format($viewCount/1000000, 1) . 'M';
     } else if ($viewCount >= 1000) {
         $formattedViews = number_format($viewCount/1000, 1) . 'K';
     } else {
         $formattedViews = $viewCount;
     }

     // Check if this is the current video being watched
     $isCurrentVideo = ($chainVideo->id == $videoId);
     $linkClass = $isCurrentVideo ? 'opacity-80 cursor-default' : 'cursor-pointer hover:bg-gray-50';
     ?>

     <!-- Video in Chain -->
     <?php if ($isCurrentVideo): ?>
         <div class="flex space-x-3 <?php echo $linkClass; ?> p-2 rounded border-l-4 border-green-500 bg-green-50">
     <?php else: ?>
         <a href="<?php echo $mainUrl;?>watch?id=<?php echo $chainVideo->id;?>">
             <div class="flex space-x-3 <?php echo $linkClass; ?> p-2 rounded">
     <?php endif; ?>

         <img src="<?php echo $chainVideo->thumbnailUrl;?>" alt="Video" class="w-20 h-14 object-cover rounded">
         <div class="flex-1">
             <h4 class="text-sm font-medium text-gray-900 line-clamp-2 mb-1">
                 <?php echo $chainVideo->title; ?>
                 <?php if ($isCurrentVideo): ?>
                     <span class="text-xs text-green-600 font-semibold ml-1">(Currently Playing)</span>
                 <?php endif; ?>
             </h4>
             <p class="text-xs text-gray-600"><?php echo $channel->name; ?></p>
             <p class="text-xs text-gray-500"><?php echo $formattedViews; ?> views</p>
         </div>

     <?php if ($isCurrentVideo): ?>
         </div>
     <?php else: ?>
             </div>
         </a>
     <?php endif; ?>

     <?php
 }
?>
         </div>
     </div>
     <?php
 } else {
     // Optional: Show a message when no video chain is found
     // You can uncomment this if you want to show something when there's no chain
     /*
     ?>
     <div class="bg-white rounded-lg p-4 mb-6">
         <h3 class="font-semibold mb-4">Video Series</h3>
         <p class="text-gray-500 text-sm">This video is not part of a series.</p>
     </div>
     <?php
     */
 }
 ?>