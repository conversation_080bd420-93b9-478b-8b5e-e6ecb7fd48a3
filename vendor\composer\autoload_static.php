<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit4389d00d716d98a86e8e7988ecbf096e
{
    public static $prefixLengthsPsr4 = array (
        'S' => 
        array (
            'SimpleCrud\\' => 11,
        ),
        'P' => 
        array (
            'Psr\\EventDispatcher\\' => 20,
            'PhpSanitization\\PhpSanitization\\' => 32,
        ),
        'A' => 
        array (
            'Atlas\\Query\\' => 12,
            'Atlas\\Pdo\\' => 10,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'SimpleCrud\\' => 
        array (
            0 => __DIR__ . '/..' . '/simple-crud/simple-crud/src',
        ),
        'Psr\\EventDispatcher\\' => 
        array (
            0 => __DIR__ . '/..' . '/psr/event-dispatcher/src',
        ),
        'PhpSanitization\\PhpSanitization\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpsanitization/phpsanitization/src',
        ),
        'Atlas\\Query\\' => 
        array (
            0 => __DIR__ . '/..' . '/atlas/query/src',
        ),
        'Atlas\\Pdo\\' => 
        array (
            0 => __DIR__ . '/..' . '/atlas/pdo/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit4389d00d716d98a86e8e7988ecbf096e::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit4389d00d716d98a86e8e7988ecbf096e::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit4389d00d716d98a86e8e7988ecbf096e::$classMap;

        }, null, ClassLoader::class);
    }
}
