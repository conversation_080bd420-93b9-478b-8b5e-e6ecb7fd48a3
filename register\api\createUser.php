<?php
include '../../includes/config.php';

$name = $_POST['name'];
$email = $_POST['email'];
$password = $_POST['password'];
//sanitize use phpSanitination Library
$emailGood = filter_var($email, FILTER_VALIDATE_EMAIL);
$nameSanitized = $sanitizer->useSanitize($name);
if($emailGood && $nameSanitized){
        $passwordEncrypted = password_hash($password, PASSWORD_DEFAULT);
        // Check if the user already exists
        $user = $db->user->get(['email' => $email]);
        if ($user) {
            echo "An account with this email address already exists.";
            exit;
        }
        // Create a new user using simple-crud
        $addUser = $db->user[] = [
            'name' => $name,
            'email' => $email,
            'password' => password_hash($password, PASSWORD_DEFAULT)
        ];

        // Return success response
        if ($addUser) {
            $user = $db->user->get(['email' => $email]);
            $_SESSION['userEmail'] = $email;
            $_SESSION['userName'] = $name;
            $_SESSION['userId'] = $user->id;
            echo "Your account has been created successfully. <script>window.location.href='../'</script>";
            exit;
        }else{
            echo "An error occurred. Please try again later.";
        }
}else{
    echo "Invalid email. Please try again.";
}
?>