<?php
require_once '../includes/config.php';
$title = 'Watch Video - Better Muslim';
include '../includes/header.php';
if (!isset($_GET['id'])) {
    header("Location: ../");
}else{
$videoId = $_GET['id'];
}
$video = $db->video[$videoId];
?>

<body class="bg-gray-50">
   <?php include '../includes/navbar.php'; ?>
    <!-- Main Content -->
    <main id="mainContent" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Video Player Section -->
            <div hx-post="api/fetchDetails.php" hx-vals='{"id":"<?php echo $videoId;?>"}' hx-target="this" hx-trigger="load" hx-swap="innerHTML" class="lg:col-span-2">
                
               
            </div>
 <?php
    if($video->beforeVideoId != 0){
    ?>
            <!-- Sidebar -->
            <div class="lg:col-span-1" hx-post="api/fetchBeforeVideo.php" hx-target="this" hx-swap="innerHTML" hx-trigger="load" hx-vals='{"id":"<?php echo $videoId;?>"}'>
               
            </div>
            <?php }?>
              
            </div>
   
        </div>
    </main>
<?php include '../includes/footer.php'; ?>